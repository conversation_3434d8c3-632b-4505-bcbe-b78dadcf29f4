import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import MainLayout from '@/components/layout/MainLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Building, Users, ClipboardList, Settings, School, FileText, CheckSquare, LayoutDashboard } from 'lucide-react';

// Import organisation components
import { UserManagement } from '@/components/organization/UserManagement';

// We'll create these components next
import OrganizationTaskManagement from '@/components/organization/OrganizationTaskManagement';
import { OrganizationSettings } from '@/components/organization/OrganizationSettings';
import SchoolsManagement from '@/components/organization/SchoolsManagement';

import ComplianceManagement from '@/components/organization/ComplianceManagement';
import OrganizationOverview from '@/components/organization/OrganizationOverview';

const OrganizationDashboard = () => {
  const { organizationId, profile, isAdmin } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const navigate = useNavigate();
  const location = useLocation();

  // Parse the tab from URL query parameters
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tabParam = searchParams.get('tab');
    if (tabParam && ['overview', 'tasks', 'compliance', 'invoices', 'users', 'schools', 'settings'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [location.search]);

  if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log('OrganizationDashboard: organizationId =', organizationId);
  }
  }

  // Determine if this is a trust admin (will need to be updated when organisation_type is added)
  // For now, we'll just check if they're an admin
  const isTrustAdmin = isAdmin && profile?.organization_id === organizationId;

  // Redirect if not an organisation admin
  React.useEffect(() => {
    if (!isAdmin || !organizationId) {
      // Redirect to dashboard or show access denied
      // This would be implemented with useNavigate from react-router-dom
    }
  }, [isAdmin, organizationId]);

  // If user is not part of an organization, show option to create one
  if (!organizationId) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card>
            <CardHeader>
              <CardTitle>No Organisation Found</CardTitle>
              <CardDescription>
                You are not currently part of any organisation.
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <p className="mb-4">Would you like to create a new organisation?</p>
              <Button
                onClick={() => navigate('/organization/dashboard?tab=settings&new=true')}
                className="mr-2"
              >
                Create New Organisation
              </Button>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  // If user is not an admin, show access denied
  if (!isAdmin) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card>
            <CardHeader>
              <CardTitle>Access Denied</CardTitle>
              <CardDescription>
                You don't have admin permissions to access the organisation dashboard.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center mb-6">
          <Building className="h-6 w-6 text-blue-500 mr-2" />
          <h1 className="text-2xl font-bold">Organisation Dashboard</h1>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="flex flex-wrap gap-1 w-full">
            <TabsTrigger value="overview" className="flex items-center flex-1">
              <LayoutDashboard className="h-4 w-4 mr-2" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="tasks" className="flex items-center flex-1">
              <ClipboardList className="h-4 w-4 mr-2" />
              <span>Tasks</span>
            </TabsTrigger>
            <TabsTrigger value="compliance" className="flex items-center flex-1">
              <CheckSquare className="h-4 w-4 mr-2" />
              <span>Compliance</span>
            </TabsTrigger>
            <TabsTrigger value="invoices" className="flex items-center flex-1">
              <FileText className="h-4 w-4 mr-2" />
              <span>Invoices</span>
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center flex-1">
              <Users className="h-4 w-4 mr-2" />
              <span>Users</span>
            </TabsTrigger>
            {isTrustAdmin && (
              <TabsTrigger value="schools" className="flex items-center flex-1">
                <School className="h-4 w-4 mr-2" />
                <span>Schools</span>
              </TabsTrigger>
            )}

            <TabsTrigger value="settings" className="flex items-center flex-1">
              <Settings className="h-4 w-4 mr-2" />
              <span>Settings</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Organisation Overview</CardTitle>
                <CardDescription>
                  Comprehensive view of your organisation's tasks, compliance, finances, and team
                </CardDescription>
              </CardHeader>
              <CardContent>
                <OrganizationOverview />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>User Management</CardTitle>
                <CardDescription>
                  Manage users and invitations for your organisation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <UserManagement />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tasks" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Task Management</CardTitle>
                <CardDescription>
                  Manage and assign tasks within your organisation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <OrganizationTaskManagement />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="compliance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Compliance Management</CardTitle>
                <CardDescription>
                  Manage regular and recurring compliance tasks that must be completed within specific cycles
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ComplianceManagement />
              </CardContent>
            </Card>
          </TabsContent>

          {isTrustAdmin && (
            <TabsContent value="schools" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Manage Schools</CardTitle>
                  <CardDescription>
                    Add, edit, and remove schools within your Multi-Academy Trust. This is for managing multiple schools that are part of your organisation.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <SchoolsManagement />
                </CardContent>
              </Card>
            </TabsContent>
          )}

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Organisation Profile</CardTitle>
                <CardDescription>
                  Update your organisation's information and settings. This is for configuring the current organisation you're managing.
                </CardDescription>
              </CardHeader>
              <CardContent>
                {(() => {
                  if (process.env.NODE_ENV === 'development') {
                    if (process.env.NODE_ENV === 'development') {
    console.log('OrganizationDashboard: Rendering OrganizationSettings with key =', organizationId);
  }
                  }
                  return null;
                })()}
                <OrganizationSettings key={organizationId} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="invoices" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Invoices</CardTitle>
                <CardDescription>
                  View and download invoices for your organisation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col space-y-4">
                  <p className="text-sm text-muted-foreground">
                    View your organisation's payment history, outstanding balances, and download invoices.
                  </p>
                  <Button
                    onClick={() => navigate('/organization/invoices')}
                    className="w-fit"
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    View All Invoices
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>


        </Tabs>
      </div>
    </MainLayout>
  );
};

export default OrganizationDashboard;
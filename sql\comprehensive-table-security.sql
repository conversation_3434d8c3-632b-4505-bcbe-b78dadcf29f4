-- COMPREHENSIVE TABLE SECURITY ENHANCEMENT
-- This script fixes critical security vulnerabilities across all public tables
-- CRITICAL: Many tables currently use 'public' role instead of 'authenticated'

-- ============================================================================
-- CRITICAL SECURITY FIXES - ALL PUBLIC TABLES
-- ============================================================================

BEGIN;

-- ============================================================================
-- 1. FIX COMPLIANCE TABLES (Currently use 'public' role - CRITICAL)
-- ============================================================================

-- Fix compliance_attachments
DROP POLICY IF EXISTS "Organization admins can manage compliance attachments" ON public.compliance_attachments;

CREATE POLICY "Organization admins can manage compliance attachments" ON public.compliance_attachments
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND (
                p.is_site_admin = true
                OR (
                    p.role = 'admin'
                    AND p.organization_id = compliance_attachments.organization_id
                )
            )
        )
    );

-- Block anonymous access
CREATE POLICY "Block anonymous access to compliance attachments" ON public.compliance_attachments
    FOR ALL TO anon USING (false);

-- Fix compliance_completions
DROP POLICY IF EXISTS "Organization admins can manage compliance completions" ON public.compliance_completions;

CREATE POLICY "Organization admins can manage compliance completions" ON public.compliance_completions
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND (
                p.is_site_admin = true
                OR (
                    p.role = 'admin'
                    AND p.organization_id = compliance_completions.organization_id
                )
            )
        )
    );

-- Block anonymous access
CREATE POLICY "Block anonymous access to compliance completions" ON public.compliance_completions
    FOR ALL TO anon USING (false);

-- Fix compliance_tasks
DROP POLICY IF EXISTS "Organization admins can manage compliance tasks" ON public.compliance_tasks;

CREATE POLICY "Organization admins can manage compliance tasks" ON public.compliance_tasks
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND (
                p.is_site_admin = true
                OR (
                    p.role = 'admin'
                    AND p.organization_id = compliance_tasks.organization_id
                )
            )
        )
    );

-- Block anonymous access
CREATE POLICY "Block anonymous access to compliance tasks" ON public.compliance_tasks
    FOR ALL TO anon USING (false);

-- ============================================================================
-- 2. FIX INVOICE TABLES (Currently use 'public' role - CRITICAL)
-- ============================================================================

-- Drop all existing public role policies
DROP POLICY IF EXISTS "Service role can update invoices" ON public.invoices;
DROP POLICY IF EXISTS "Admins can update invoices" ON public.invoices;
DROP POLICY IF EXISTS "Service role can insert invoices" ON public.invoices;
DROP POLICY IF EXISTS "Users can view invoices for their payments" ON public.invoices;
DROP POLICY IF EXISTS "Admins can insert invoices" ON public.invoices;

-- Create secure authenticated policies
CREATE POLICY "Service role can manage invoices" ON public.invoices
    FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Users can view their own invoices" ON public.invoices
    FOR SELECT TO authenticated
    USING (
        user_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND (
                p.is_site_admin = true
                OR (
                    p.role = 'admin'
                    AND p.organization_id = (
                        SELECT organization_id FROM public.profiles
                        WHERE id = invoices.user_id
                    )
                )
            )
        )
    );

CREATE POLICY "Admins can manage invoices" ON public.invoices
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND (
                p.is_site_admin = true
                OR (
                    p.role = 'admin'
                    AND p.organization_id = (
                        SELECT organization_id FROM public.profiles
                        WHERE id = invoices.user_id
                    )
                )
            )
        )
    );

-- Block anonymous access
CREATE POLICY "Block anonymous access to invoices" ON public.invoices
    FOR ALL TO anon USING (false);

-- ============================================================================
-- 3. FIX NOTIFICATIONS (Currently use 'public' role - CRITICAL)
-- ============================================================================

-- Drop all existing public role policies
DROP POLICY IF EXISTS "Users can update their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can view their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Authenticated users can create notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can insert notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can delete their own notifications" ON public.notifications;
DROP POLICY IF EXISTS "Service role can manage all notifications" ON public.notifications;

-- Create secure authenticated policies
CREATE POLICY "Service role can manage all notifications" ON public.notifications
    FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Users can manage their own notifications" ON public.notifications
    FOR ALL TO authenticated
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Admins can view org notifications" ON public.notifications
    FOR SELECT TO authenticated
    USING (
        user_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND (
                p.is_site_admin = true
                OR (
                    p.role = 'admin'
                    AND p.organization_id = (
                        SELECT organization_id FROM public.profiles
                        WHERE id = notifications.user_id
                    )
                )
            )
        )
    );

-- Block anonymous access
CREATE POLICY "Block anonymous access to notifications" ON public.notifications
    FOR ALL TO anon USING (false);

-- ============================================================================
-- 4. FIX PAYMENTS (Currently use 'public' role - CRITICAL)
-- ============================================================================

-- Drop existing public role policies
DROP POLICY IF EXISTS "Users can view payments they are involved in" ON public.payments;
DROP POLICY IF EXISTS "Admins can update payments" ON public.payments;
DROP POLICY IF EXISTS "Admins can insert payments" ON public.payments;

-- Create secure authenticated policies
CREATE POLICY "Service role can manage payments" ON public.payments
    FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Users can view their own payments" ON public.payments
    FOR SELECT TO authenticated
    USING (
        payer_id = auth.uid()
        OR payee_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND (
                p.is_site_admin = true
                OR (
                    p.role = 'admin'
                    AND (
                        p.organization_id = (
                            SELECT organization_id FROM public.profiles WHERE id = payments.payer_id
                        )
                        OR p.organization_id = (
                            SELECT organization_id FROM public.profiles WHERE id = payments.payee_id
                        )
                    )
                )
            )
        )
    );

CREATE POLICY "Admins can manage payments" ON public.payments
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND (
                p.is_site_admin = true
                OR (
                    p.role = 'admin'
                    AND (
                        p.organization_id = (
                            SELECT organization_id FROM public.profiles WHERE id = payments.payer_id
                        )
                        OR p.organization_id = (
                            SELECT organization_id FROM public.profiles WHERE id = payments.payee_id
                        )
                    )
                )
            )
        )
    );

-- Block anonymous access
CREATE POLICY "Block anonymous access to payments" ON public.payments
    FOR ALL TO anon USING (false);

-- ============================================================================
-- 5. ADD ANONYMOUS BLOCKING TO ALL REMAINING TABLES
-- ============================================================================

-- Block anonymous access to all sensitive tables that don't have it
CREATE POLICY "Block anonymous access to chat_threads" ON public.chat_threads
    FOR ALL TO anon USING (false);

CREATE POLICY "Block anonymous access to offers" ON public.offers
    FOR ALL TO anon USING (false);

CREATE POLICY "Block anonymous access to organizations" ON public.organizations
    FOR ALL TO anon USING (false);

CREATE POLICY "Block anonymous access to stripe_accounts" ON public.stripe_accounts
    FOR ALL TO anon USING (false);

CREATE POLICY "Block anonymous access to stripe_customers" ON public.stripe_customers
    FOR ALL TO anon USING (false);

CREATE POLICY "Block anonymous access to support_requests" ON public.support_requests
    FOR ALL TO anon USING (false);

CREATE POLICY "Block anonymous access to task_messages" ON public.task_messages
    FOR ALL TO anon USING (false);

CREATE POLICY "Block anonymous access to tasks" ON public.tasks
    FOR ALL TO anon USING (false);

-- Note: user_invitations already has controlled anon access for token-based signup

-- ============================================================================
-- 6. FIX STRIPE TABLES (Currently use 'public' role - CRITICAL)
-- ============================================================================

-- Fix stripe_accounts - drop public policies
DROP POLICY IF EXISTS "Users can update their own stripe account" ON public.stripe_accounts;
DROP POLICY IF EXISTS "System can insert stripe accounts" ON public.stripe_accounts;
DROP POLICY IF EXISTS "Users can view their own stripe account" ON public.stripe_accounts;

-- Create secure authenticated policies for stripe_accounts
CREATE POLICY "Service role can manage stripe accounts" ON public.stripe_accounts
    FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Users can manage their own stripe account" ON public.stripe_accounts
    FOR ALL TO authenticated
    USING (
        user_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND p.is_site_admin = true
        )
    )
    WITH CHECK (
        user_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND p.is_site_admin = true
        )
    );

-- Fix stripe_customers - drop public policies
DROP POLICY IF EXISTS "stripe_customers_select_policy" ON public.stripe_customers;
DROP POLICY IF EXISTS "stripe_customers_service_policy" ON public.stripe_customers;
DROP POLICY IF EXISTS "stripe_customers_update_policy" ON public.stripe_customers;
DROP POLICY IF EXISTS "stripe_customers_delete_policy" ON public.stripe_customers;
DROP POLICY IF EXISTS "stripe_customers_insert_policy" ON public.stripe_customers;

-- Create secure authenticated policies for stripe_customers
CREATE POLICY "Service role can manage stripe customers" ON public.stripe_customers
    FOR ALL TO service_role USING (true) WITH CHECK (true);

CREATE POLICY "Users can manage their own stripe customer" ON public.stripe_customers
    FOR ALL TO authenticated
    USING (
        user_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND p.is_site_admin = true
        )
    )
    WITH CHECK (
        user_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND p.is_site_admin = true
        )
    );

-- ============================================================================
-- 7. FIX USER INVITATIONS (Currently use 'public' role - CRITICAL)
-- ============================================================================

-- Drop public role policies (keep the anon policy for token-based signup)
DROP POLICY IF EXISTS "Organization admins can create invitations" ON public.user_invitations;
DROP POLICY IF EXISTS "Organization admins can update invitations" ON public.user_invitations;
DROP POLICY IF EXISTS "Organization admins can view invitations" ON public.user_invitations;

-- Create secure authenticated policies
CREATE POLICY "Organization admins can manage invitations" ON public.user_invitations
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND (
                p.is_site_admin = true
                OR (
                    p.role = 'admin'
                    AND p.organization_id = user_invitations.organization_id
                )
            )
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles p
            WHERE p.id = auth.uid()
            AND (
                p.is_site_admin = true
                OR (
                    p.role = 'admin'
                    AND p.organization_id = user_invitations.organization_id
                )
            )
        )
    );

-- ============================================================================
-- 8. VERIFICATION AND AUDIT
-- ============================================================================

-- Verify no tables have public role policies (except where intended)
SELECT
    tablename,
    policyname,
    roles,
    cmd
FROM pg_policies
WHERE schemaname = 'public'
AND 'public' = ANY(roles)
AND tablename NOT IN ('profiles') -- profiles has legitimate public INSERT for signup
ORDER BY tablename, policyname;

-- Verify all tables have anonymous blocking
SELECT
    t.tablename,
    CASE
        WHEN EXISTS (
            SELECT 1 FROM pg_policies p
            WHERE p.tablename = t.tablename
            AND 'anon' = ANY(p.roles)
            AND p.qual = 'false'
        ) THEN 'BLOCKED'
        WHEN EXISTS (
            SELECT 1 FROM pg_policies p
            WHERE p.tablename = t.tablename
            AND 'anon' = ANY(p.roles)
        ) THEN 'PARTIAL_ACCESS'
        ELSE 'NO_ANON_POLICY'
    END as anon_status
FROM pg_tables t
WHERE t.schemaname = 'public'
ORDER BY anon_status DESC, t.tablename;

-- Count policies per table after fixes
SELECT
    tablename,
    COUNT(*) as total_policies,
    COUNT(CASE WHEN 'authenticated' = ANY(roles) THEN 1 END) as authenticated_policies,
    COUNT(CASE WHEN 'service_role' = ANY(roles) THEN 1 END) as service_role_policies,
    COUNT(CASE WHEN 'anon' = ANY(roles) THEN 1 END) as anon_policies,
    COUNT(CASE WHEN 'public' = ANY(roles) THEN 1 END) as public_policies
FROM pg_policies
WHERE schemaname = 'public'
GROUP BY tablename
ORDER BY public_policies DESC, tablename;

COMMIT;

-- ============================================================================
-- POST-EXECUTION SECURITY CHECKLIST
-- ============================================================================

/*
After running this script, verify:

CRITICAL FIXES APPLIED:
✅ All compliance tables now use 'authenticated' instead of 'public'
✅ All invoice operations now use 'authenticated' instead of 'public'
✅ All notification operations now use 'authenticated' instead of 'public'
✅ All payment operations now use 'authenticated' instead of 'public'
✅ All Stripe operations now use 'authenticated' instead of 'public'
✅ All user invitation operations now use 'authenticated' instead of 'public'
✅ Anonymous access blocked on all sensitive tables

SECURITY VERIFICATION:
✅ No unauthorized cross-organization data access
✅ Anonymous users cannot access sensitive data
✅ Service role maintains necessary access for Edge Functions
✅ User signup still works (profiles table public INSERT preserved)
✅ Token-based invitations still work (controlled anon access preserved)

APPLICATION TESTING REQUIRED:
1. Test user authentication and signup
2. Test all major application features
3. Test cross-organization isolation
4. Test admin functions
5. Test payment processing
6. Test notification system
7. Test compliance features
8. Test Stripe integration

If any functionality breaks, individual policies can be adjusted while
maintaining the security improvements.
*/

-- ENHANCE RLS SECURITY FOR EXISTING TABLES
-- This script strengthens existing RLS policies without removing views
-- Safe approach that adds additional security layers

-- ============================================================================
-- RLS SECURITY ENHANCEMENT - CONSERVATIVE APPROACH
-- ============================================================================

BEGIN;

-- ============================================================================
-- 1. STRENGTHEN SECURITY EVENTS TABLE
-- ============================================================================

-- Current issue: security_events allows public INSERT and has weak SELECT policy
-- Fix: Restrict INSERT to service_role and authenticated users only

-- Remove overly permissive public INSERT policy
DROP POLICY IF EXISTS "Site admins can insert security events" ON public.security_events;

-- Add more secure INSERT policy for Edge Functions (service_role)
CREATE POLICY "Service role can insert security events" ON public.security_events
    FOR INSERT 
    TO service_role
    WITH CHECK (true);

-- Add secure INSERT policy for authenticated users (for manual testing)
CREATE POLICY "Site admins can insert security events" ON public.security_events
    FOR INSERT 
    TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_site_admin = true
        )
    );

-- Strengthen SELECT policy to be more explicit
DROP POLICY IF EXISTS "Site admins can view all security events" ON public.security_events;

CREATE POLICY "Site admins can view all security events" ON public.security_events
    FOR SELECT 
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_site_admin = true
        )
    );

-- Block anonymous access completely
CREATE POLICY "Block anonymous access to security events" ON public.security_events
    FOR ALL 
    TO anon
    USING (false);

-- ============================================================================
-- 2. STRENGTHEN SECURITY INCIDENTS TABLE  
-- ============================================================================

-- Current policies are good but let's add explicit anonymous blocking

-- Block anonymous access completely
CREATE POLICY "Block anonymous access to security incidents" ON public.security_incidents
    FOR ALL 
    TO anon
    USING (false);

-- Ensure service role has full access for Edge Functions
CREATE POLICY "Service role full access to security incidents" ON public.security_incidents
    FOR ALL 
    TO service_role
    USING (true)
    WITH CHECK (true);

-- ============================================================================
-- 3. ADD ADDITIONAL ORGANIZATION ISOLATION
-- ============================================================================

-- Add extra protection to ensure cross-organization data isolation
-- This adds defense-in-depth to existing policies

-- Strengthen profiles organization isolation
CREATE POLICY "Strict organization isolation for profiles" ON public.profiles
    FOR SELECT 
    TO authenticated
    USING (
        -- User can see their own profile
        id = auth.uid() 
        OR 
        -- Site admins can see all
        is_site_admin_direct(auth.uid())
        OR
        -- Users can only see profiles in their organization hierarchy
        (
            organization_id IN (
                SELECT DISTINCT org_id FROM (
                    -- User's own organization
                    SELECT organization_id as org_id FROM public.profiles WHERE id = auth.uid()
                    UNION
                    -- If user is trust admin, include child schools
                    SELECT o.id as org_id 
                    FROM public.organizations o
                    JOIN public.profiles p ON p.organization_id = o.parent_organization_id
                    WHERE p.id = auth.uid() AND p.role = 'admin'
                ) allowed_orgs
            )
            AND 
            -- Additional role-based restrictions
            (
                -- Admins can see all in their org
                EXISTS (
                    SELECT 1 FROM public.profiles p 
                    WHERE p.id = auth.uid() 
                    AND p.role = 'admin'
                )
                OR
                -- Non-admins can only see specific roles
                role IN ('teacher', 'maintenance', 'support')
            )
        )
    );

-- ============================================================================
-- 4. STRENGTHEN TASK ISOLATION
-- ============================================================================

-- Add additional task isolation policy for extra security
CREATE POLICY "Enhanced task organization isolation" ON public.tasks
    FOR SELECT 
    TO authenticated
    USING (
        -- Site admins can see all
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND is_site_admin = true
        )
        OR
        -- Users can only see tasks in their organization
        organization_id IN (
            SELECT organization_id FROM public.profiles WHERE id = auth.uid()
        )
        OR
        -- Trust admins can see tasks in child organizations
        (
            EXISTS (
                SELECT 1 FROM public.profiles p
                JOIN public.organizations o ON tasks.organization_id = o.id
                WHERE p.id = auth.uid() 
                AND p.role = 'admin'
                AND o.parent_organization_id = p.organization_id
            )
        )
    );

-- ============================================================================
-- 5. ADD AUDIT LOGGING TRIGGERS
-- ============================================================================

-- Create audit log for sensitive table access
CREATE OR REPLACE FUNCTION log_sensitive_access()
RETURNS TRIGGER AS $$
BEGIN
    -- Log access to sensitive tables for security monitoring
    INSERT INTO public.security_events (
        type,
        severity,
        action,
        user_email,
        resource,
        details,
        timestamp
    ) VALUES (
        'data_access',
        'low',
        TG_OP || '_' || TG_TABLE_NAME,
        (SELECT email FROM auth.users WHERE id = auth.uid()),
        TG_TABLE_NAME || '_' || COALESCE(NEW.id::text, OLD.id::text),
        jsonb_build_object(
            'table', TG_TABLE_NAME,
            'operation', TG_OP,
            'user_id', auth.uid(),
            'timestamp', NOW()
        ),
        NOW()
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Apply audit logging to sensitive operations (optional - can be enabled later)
-- Uncomment these if you want detailed audit logging:

-- CREATE TRIGGER audit_security_events_access
--     AFTER INSERT OR UPDATE OR DELETE ON public.security_events
--     FOR EACH ROW EXECUTE FUNCTION log_sensitive_access();

-- CREATE TRIGGER audit_profiles_access  
--     AFTER UPDATE OR DELETE ON public.profiles
--     FOR EACH ROW EXECUTE FUNCTION log_sensitive_access();

-- ============================================================================
-- 6. VERIFICATION QUERIES
-- ============================================================================

-- Verify RLS is enabled on all tables
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = t.tablename) as policy_count
FROM pg_tables t
WHERE schemaname = 'public' 
AND tablename IN ('security_events', 'security_incidents', 'profiles', 'tasks', 'organizations')
ORDER BY tablename;

-- Count policies per table
SELECT 
    tablename,
    COUNT(*) as total_policies,
    COUNT(CASE WHEN cmd = 'SELECT' THEN 1 END) as select_policies,
    COUNT(CASE WHEN cmd = 'INSERT' THEN 1 END) as insert_policies,
    COUNT(CASE WHEN cmd = 'UPDATE' THEN 1 END) as update_policies,
    COUNT(CASE WHEN cmd = 'DELETE' THEN 1 END) as delete_policies,
    COUNT(CASE WHEN cmd = 'ALL' THEN 1 END) as all_policies
FROM pg_policies 
WHERE tablename IN ('security_events', 'security_incidents', 'profiles', 'tasks', 'organizations')
GROUP BY tablename
ORDER BY tablename;

COMMIT;

-- ============================================================================
-- POST-EXECUTION VERIFICATION
-- ============================================================================

/*
After running this script, verify:

1. ✅ All tables still have RLS enabled
2. ✅ Security events table has stricter access controls
3. ✅ Anonymous users cannot access sensitive data
4. ✅ Organization isolation is strengthened
5. ✅ Application functionality unchanged
6. ✅ Security dashboard still works
7. ✅ Edge Functions can still write security events

Test with different user roles:
- Site admin: Should see all data
- Organization admin: Should see only their org data  
- Regular user: Should see limited data
- Anonymous: Should see nothing sensitive
*/

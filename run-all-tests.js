#!/usr/bin/env node
// Comprehensive Test Runner for Classtasker Security & Load Testing

import { execSync } from 'child_process'
import { existsSync } from 'fs'
import path from 'path'

class TestRunner {
  constructor() {
    this.results = {
      security: { passed: 0, failed: 0, warnings: 0 },
      load: { passed: 0, failed: 0, warnings: 0 },
      overall: 'PENDING'
    }
  }

  async runAllTests() {
    console.log('🚀 CLASSTASKER COMPREHENSIVE TESTING SUITE')
    console.log('=' * 60)
    console.log('🔒 Security Tests + ⚡ Load Tests + 🛡️ Penetration Tests')
    console.log('=' * 60)
    
    try {
      // 1. Security Tests
      await this.runSecurityTests()
      
      // 2. Load Tests  
      await this.runLoadTests()
      
      // 3. Generate Report
      this.generateReport()
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message)
      process.exit(1)
    }
  }

  async runSecurityTests() {
    console.log('\n🔒 RUNNING SECURITY TESTS...')
    console.log('-' * 40)
    
    try {
      // RLS Policy Tests
      console.log('🔍 Testing Row Level Security Policies...')
      await this.runCommand('node security-tests/test-rls-policies.js')
      this.results.security.passed++
      
      // Frontend Security Tests
      console.log('🌐 Testing Frontend Security...')
      await this.runCommand('node security-tests/test-frontend-security.js')
      this.results.security.passed++
      
      // OWASP ZAP Scan (if available)
      if (this.checkToolAvailable('zap-baseline.py')) {
        console.log('🕷️ Running OWASP ZAP Security Scan...')
        await this.runCommand('zap-baseline.py -t https://classtasker.com -J zap-report.json')
        this.results.security.passed++
      } else {
        console.log('⚠️ OWASP ZAP not available - install for comprehensive security scanning')
        this.results.security.warnings++
      }
      
      // Nuclei Scan (if available)
      if (this.checkToolAvailable('nuclei')) {
        console.log('🎯 Running Nuclei Vulnerability Scan...')
        await this.runCommand('nuclei -u https://classtasker.com -o nuclei-report.txt')
        this.results.security.passed++
      } else {
        console.log('⚠️ Nuclei not available - install for vulnerability scanning')
        this.results.security.warnings++
      }
      
    } catch (error) {
      console.error('❌ Security test failed:', error.message)
      this.results.security.failed++
    }
  }

  async runLoadTests() {
    console.log('\n⚡ RUNNING LOAD TESTS...')
    console.log('-' * 40)
    
    try {
      // Database Load Tests
      console.log('🗄️ Testing Database Performance...')
      await this.runCommand('node load-tests/database-load-test.js')
      this.results.load.passed++
      
      // K6 Load Tests (if available)
      if (this.checkToolAvailable('k6')) {
        console.log('📊 Running K6 Load Tests...')
        await this.runCommand('k6 run load-tests/k6-load-test.js')
        this.results.load.passed++
      } else {
        console.log('⚠️ K6 not available - install for comprehensive load testing')
        this.results.load.warnings++
      }
      
      // Artillery Load Tests (if available)
      if (this.checkToolAvailable('artillery')) {
        console.log('🎯 Running Artillery Load Tests...')
        await this.runCommand('artillery run load-tests/artillery-config.yml')
        this.results.load.passed++
      } else {
        console.log('⚠️ Artillery not available - install for additional load testing')
        this.results.load.warnings++
      }
      
    } catch (error) {
      console.error('❌ Load test failed:', error.message)
      this.results.load.failed++
    }
  }

  async runCommand(command) {
    try {
      const output = execSync(command, { 
        encoding: 'utf8', 
        stdio: 'pipe',
        timeout: 300000 // 5 minute timeout
      })
      console.log('✅ Command completed successfully')
      return output
    } catch (error) {
      console.error(`❌ Command failed: ${command}`)
      throw error
    }
  }

  checkToolAvailable(tool) {
    try {
      execSync(`${tool} --version`, { stdio: 'ignore' })
      return true
    } catch {
      return false
    }
  }

  generateReport() {
    console.log('\n📊 TEST RESULTS SUMMARY')
    console.log('=' * 60)
    
    // Security Results
    console.log('🔒 SECURITY TESTS:')
    console.log(`   ✅ Passed: ${this.results.security.passed}`)
    console.log(`   ❌ Failed: ${this.results.security.failed}`)
    console.log(`   ⚠️  Warnings: ${this.results.security.warnings}`)
    
    // Load Test Results
    console.log('\n⚡ LOAD TESTS:')
    console.log(`   ✅ Passed: ${this.results.load.passed}`)
    console.log(`   ❌ Failed: ${this.results.load.failed}`)
    console.log(`   ⚠️  Warnings: ${this.results.load.warnings}`)
    
    // Overall Assessment
    const totalFailed = this.results.security.failed + this.results.load.failed
    const totalPassed = this.results.security.passed + this.results.load.passed
    const totalWarnings = this.results.security.warnings + this.results.load.warnings
    
    console.log('\n🎯 OVERALL ASSESSMENT:')
    if (totalFailed === 0 && totalPassed > 0) {
      this.results.overall = 'EXCELLENT'
      console.log('   🟢 EXCELLENT - All tests passed!')
    } else if (totalFailed === 0 && totalWarnings > 0) {
      this.results.overall = 'GOOD'
      console.log('   🟡 GOOD - Tests passed with some warnings')
    } else if (totalFailed > 0 && totalFailed < totalPassed) {
      this.results.overall = 'NEEDS_IMPROVEMENT'
      console.log('   🟠 NEEDS IMPROVEMENT - Some tests failed')
    } else {
      this.results.overall = 'CRITICAL'
      console.log('   🔴 CRITICAL - Multiple test failures detected')
    }
    
    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:')
    
    if (this.results.security.failed > 0) {
      console.log('   🔒 Address security vulnerabilities immediately')
    }
    
    if (this.results.load.failed > 0) {
      console.log('   ⚡ Optimize performance bottlenecks')
    }
    
    if (totalWarnings > 0) {
      console.log('   🛠️  Install missing testing tools for comprehensive coverage:')
      if (!this.checkToolAvailable('k6')) {
        console.log('      - K6: https://k6.io/docs/getting-started/installation/')
      }
      if (!this.checkToolAvailable('artillery')) {
        console.log('      - Artillery: npm install -g artillery')
      }
      if (!this.checkToolAvailable('zap-baseline.py')) {
        console.log('      - OWASP ZAP: https://www.zaproxy.org/download/')
      }
      if (!this.checkToolAvailable('nuclei')) {
        console.log('      - Nuclei: https://github.com/projectdiscovery/nuclei')
      }
    }
    
    console.log('\n📁 GENERATED REPORTS:')
    console.log('   - zap-report.json (if ZAP was run)')
    console.log('   - nuclei-report.txt (if Nuclei was run)')
    console.log('   - k6-results.json (if K6 was run)')
    
    console.log('\n✅ Testing suite completed!')
    console.log('=' * 60)
  }
}

// Installation Guide
function showInstallationGuide() {
  console.log('📦 TESTING TOOLS INSTALLATION GUIDE')
  console.log('=' * 50)
  console.log('')
  console.log('🔧 Required Node.js packages:')
  console.log('npm install @supabase/supabase-js puppeteer')
  console.log('')
  console.log('⚡ Load Testing Tools:')
  console.log('npm install -g k6 artillery')
  console.log('')
  console.log('🔒 Security Testing Tools:')
  console.log('# OWASP ZAP: https://www.zaproxy.org/download/')
  console.log('# Nuclei: https://github.com/projectdiscovery/nuclei')
  console.log('')
  console.log('🚀 Run tests with: node run-all-tests.js')
}

// Main execution
const args = process.argv.slice(2)

if (args.includes('--help') || args.includes('-h')) {
  showInstallationGuide()
} else if (args.includes('--install-guide')) {
  showInstallationGuide()
} else {
  const runner = new TestRunner()
  runner.runAllTests()
}

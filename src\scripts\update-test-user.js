// <PERSON>ript to update the test user with an organization
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateTestUser() {
  try {
    if (process.env.NODE_ENV === 'development') {

      console.log('Updating test user...');
    

      }
    // Find the test user
    const { data: { users }, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      console.error('Error fetching users:', usersError);
      return;
    }
    
    const testUser = users.find(u => u.email === '<EMAIL>');
    
    if (!testUser) {
      console.error('Test user not found');
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log(`Found test user: ${testUser.id} (${testUser.email})`);
    

    
      }
    // Find the Test School organization
    const { data: orgs, error: orgsError } = await supabase
      .from('organizations')
      .select('*')
      .eq('name', 'Test School');
    
    if (orgsError) {
      console.error('Error fetching organizations:', orgsError);
      return;
    }
    
    if (!orgs || orgs.length === 0) {
      console.error('Test School organization not found');
      return;
    }
    
    const testSchool = orgs[0];
    if (process.env.NODE_ENV === 'development') {

      console.log(`Found Test School organization: ${testSchool.id} (${testSchool.name})`);
    

      }
    // Update the user's profile
    const { data: updatedProfile, error: updateError } = await supabase
      .from('profiles')
      .update({
        organization_id: testSchool.id,
        role: 'teacher'
      })
      .eq('id', testUser.id)
      .select()
      .single();
    
    if (updateError) {
      console.error('Error updating profile:', updateError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log(`Updated profile for ${testUser.email}:`);

    
      }
    if (process.env.NODE_ENV === 'development') {
    console.log(`Organization ID: ${updatedProfile.organization_id}`);
  }
    if (process.env.NODE_ENV === 'development') {
    console.log(`Role: ${updatedProfile.role}`);
  }
    // Also update the user metadata for backward compatibility
    const { error: metadataError } = await supabase.auth.admin.updateUserById(
      testUser.id,
      {
        user_metadata: {
          ...testUser.user_metadata,
          role: 'teacher',
          organization: {
            id: testSchool.id,
            name: testSchool.name,
            created_at: testSchool.created_at,
            updated_at: testSchool.updated_at
          }
        }
      }
    );
    
    if (metadataError) {
      console.error('Error updating user metadata:', metadataError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log('Updated user metadata successfully');

    
      }
    if (process.env.NODE_ENV === 'development') {

      console.log('Test user has been successfully linked to the Test School organization');
    

      }
  } catch (error) {
    console.error('Error updating test user:', error);
  }
}

updateTestUser();

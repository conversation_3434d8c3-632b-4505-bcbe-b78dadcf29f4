
export type NotificationType = 'offer' | 'message' | 'system' | 'task_update' | 'admin_review';
export type RelatedType = 'task' | 'offer' | 'message' | null;

export interface Notification {
  id: string;
  type: NotificationType;
  message: string;
  time: string; // Formatted time (e.g., "2 hours ago")
  read: boolean;
  relatedId?: string;
  relatedType?: RelatedType;
  createdAt?: string; // ISO timestamp
}

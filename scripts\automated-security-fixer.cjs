#!/usr/bin/env node

/**
 * Automated Security Fixer for Console Log Vulnerabilities
 * Automatically fixes all security issues found by the scanner
 */

const fs = require('fs');
const path = require('path');

// Colors for output
const colors = {
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

/**
 * Security fix patterns
 */
const SECURITY_FIXES = [
  // Fix 1: Wrap unprotected console.log with development check
  {
    pattern: /^(\s*)console\.log\(/gm,
    replacement: '$1if (process.env.NODE_ENV === \'development\') {\n$1  console.log(',
    name: 'Wrap console.log with development check',
    category: 'UNPROTECTED_LOGGING'
  },
  
  // Fix 2: Close the development check bracket
  {
    pattern: /^(\s*)console\.log\([^;]*\);(\s*)$/gm,
    replacement: (match, indent, trailing) => {
      // Count opening and closing parentheses to find the end
      let openParens = 0;
      let closeParens = 0;
      for (let char of match) {
        if (char === '(') openParens++;
        if (char === ')') closeParens++;
      }
      
      if (openParens === closeParens) {
        return match + '\n' + indent + '}';
      }
      return match;
    },
    name: 'Close development check brackets',
    category: 'UNPROTECTED_LOGGING'
  },
  
  // Fix 3: Sanitize user ID logging
  {
    pattern: /console\.log\(([^)]*)[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}([^)]*)\)/gi,
    replacement: 'console.log($1[USER_ID_REDACTED]$2)',
    name: 'Redact user IDs in console logs',
    category: 'USER_DATA'
  },
  
  // Fix 4: Sanitize email addresses
  {
    pattern: /console\.log\(([^)]*)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}([^)]*)\)/gi,
    replacement: 'console.log($1[EMAIL_REDACTED]$2)',
    name: 'Redact email addresses in console logs',
    category: 'USER_DATA'
  },
  
  // Fix 5: Sanitize API key logging
  {
    pattern: /console\.log\(([^)]*['"`])([A-Za-z0-9]{20,})(['"`][^)]*)\)/gi,
    replacement: 'console.log($1[API_KEY_REDACTED]$3)',
    name: 'Redact API keys in console logs',
    category: 'CREDENTIALS'
  },
  
  // Fix 6: Replace user object logging with safe flags
  {
    pattern: /console\.log\(([^)]*user[^)]*)\)/gi,
    replacement: 'console.log($1.replace(/user.*/, \'hasUser: \' + !!user))',
    name: 'Replace user object with safe flag',
    category: 'USER_DATA'
  }
];

/**
 * Files to exclude from fixing
 */
const EXCLUDE_PATTERNS = [
  /node_modules/,
  /\.git/,
  /dist/,
  /build/,
  /\.env/,
  /\.log$/,
  /package-lock\.json$/,
  /yarn\.lock$/,
  /automated-security-fixer\.cjs$/,
  /comprehensive-security-scanner\.cjs$/
];

/**
 * File extensions to fix
 */
const FIX_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

/**
 * Fix results storage
 */
let fixResults = {
  totalFiles: 0,
  fixedFiles: 0,
  totalFixes: 0,
  fixesByCategory: {},
  errors: []
};

/**
 * Log with colors
 */
function log(level, message) {
  const color = {
    ERROR: colors.red,
    WARNING: colors.yellow,
    SUCCESS: colors.green,
    INFO: colors.blue
  }[level] || colors.reset;
  
  console.log(`${color}[${level}]${colors.reset} ${message}`);
}

/**
 * Check if file should be excluded
 */
function shouldExcludeFile(filePath) {
  return EXCLUDE_PATTERNS.some(pattern => pattern.test(filePath));
}

/**
 * Check if file should be fixed
 */
function shouldFixFile(filePath) {
  const ext = path.extname(filePath);
  return FIX_EXTENSIONS.includes(ext) && !shouldExcludeFile(filePath);
}

/**
 * Apply security fixes to file content
 */
function applySecurityFixes(content, filePath) {
  let modifiedContent = content;
  let fileFixCount = 0;
  
  // Apply each security fix
  SECURITY_FIXES.forEach(({ pattern, replacement, name, category }) => {
    const beforeLength = modifiedContent.length;
    
    if (typeof replacement === 'function') {
      modifiedContent = modifiedContent.replace(pattern, replacement);
    } else {
      modifiedContent = modifiedContent.replace(pattern, replacement);
    }
    
    const afterLength = modifiedContent.length;
    
    if (beforeLength !== afterLength) {
      fileFixCount++;
      fixResults.totalFixes++;
      
      if (!fixResults.fixesByCategory[category]) {
        fixResults.fixesByCategory[category] = 0;
      }
      fixResults.fixesByCategory[category]++;
      
      log('INFO', `Applied fix: ${name} in ${filePath}`);
    }
  });
  
  return { content: modifiedContent, fixCount: fileFixCount };
}

/**
 * Fix a single file
 */
function fixFile(filePath) {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    const { content: fixedContent, fixCount } = applySecurityFixes(originalContent, filePath);
    
    if (fixCount > 0) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      fixResults.fixedFiles++;
      log('SUCCESS', `Fixed ${fixCount} issues in ${filePath}`);
    }
    
    return true;
  } catch (error) {
    const errorMsg = `Failed to fix ${filePath}: ${error.message}`;
    fixResults.errors.push(errorMsg);
    log('ERROR', errorMsg);
    return false;
  }
}

/**
 * Recursively fix directory
 */
function fixDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !shouldExcludeFile(fullPath)) {
        fixDirectory(fullPath);
      } else if (stat.isFile() && shouldFixFile(fullPath)) {
        fixResults.totalFiles++;
        fixFile(fullPath);
      }
    });
  } catch (error) {
    const errorMsg = `Failed to fix directory ${dirPath}: ${error.message}`;
    fixResults.errors.push(errorMsg);
    log('ERROR', errorMsg);
  }
}

/**
 * Generate fix report
 */
function generateReport() {
  console.log('\n' + '='.repeat(80));
  log('INFO', colors.bold + 'AUTOMATED SECURITY FIX RESULTS' + colors.reset);
  console.log('='.repeat(80));
  
  // Summary
  console.log(`\n📊 ${colors.bold}FIX SUMMARY${colors.reset}`);
  console.log(`Total files processed: ${fixResults.totalFiles}`);
  console.log(`Files with fixes applied: ${fixResults.fixedFiles}`);
  console.log(`Total security fixes applied: ${fixResults.totalFixes}`);
  
  // Category breakdown
  console.log(`\n🔧 ${colors.bold}FIXES BY CATEGORY${colors.reset}`);
  Object.entries(fixResults.fixesByCategory).forEach(([category, count]) => {
    console.log(`${category}: ${count} fixes`);
  });
  
  // Errors
  if (fixResults.errors.length > 0) {
    console.log(`\n❌ ${colors.bold}ERRORS${colors.reset}`);
    fixResults.errors.forEach(error => {
      console.log(`- ${error}`);
    });
  }
  
  // Success message
  if (fixResults.totalFixes > 0) {
    log('SUCCESS', `🎉 Successfully applied ${fixResults.totalFixes} security fixes!`);
    console.log('\n💡 Next steps:');
    console.log('1. Review the changes to ensure they look correct');
    console.log('2. Test your application to ensure it still works');
    console.log('3. Run the security scanner again to verify fixes');
    console.log('4. Commit the security improvements');
  } else {
    log('INFO', 'No security fixes were needed - your code is already secure!');
  }
}

/**
 * Main execution
 */
function main() {
  console.log(`${colors.blue}🔧 Starting Automated Security Fixes...${colors.reset}\n`);
  
  const startTime = Date.now();
  
  // Fix src directory
  if (fs.existsSync('src')) {
    log('INFO', 'Fixing src/ directory...');
    fixDirectory('src');
  }
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  console.log(`\n⏱️ Fixes completed in ${duration} seconds`);
  
  // Generate report
  generateReport();
  
  // Save results to file
  const reportFile = 'security-fix-results.json';
  fs.writeFileSync(reportFile, JSON.stringify(fixResults, null, 2));
  log('INFO', `Detailed results saved to ${reportFile}`);
  
  // Exit with appropriate code
  const hasErrors = fixResults.errors.length > 0;
  process.exit(hasErrors ? 1 : 0);
}

// Run the fixer
main();

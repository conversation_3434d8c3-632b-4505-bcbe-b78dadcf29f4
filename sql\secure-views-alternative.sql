-- ALTERNATIVE: Secure the Views Instead of Removing Them
-- This script converts SECURITY DEFINER views to regular views with proper RLS
-- Use this if you want to keep the views for potential future use

-- ============================================================================
-- SECURITY ADVISOR FIX: Convert SECURITY DEFINER Views to Secure Views
-- ============================================================================

BEGIN;

-- 1. Recreate security_dashboard_view without SECURITY DEFINER
DROP VIEW IF EXISTS public.security_dashboard_view CASCADE;

CREATE VIEW public.security_dashboard_view AS
SELECT 
    se.id,
    se.type,
    se.severity,
    se.user_email,
    se.action,
    se.resource,
    se.ip_address,
    se.timestamp,
    se.details,
    CASE 
        WHEN se.severity = 'critical' THEN 1
        WHEN se.severity = 'high' THEN 2
        WHEN se.severity = 'medium' THEN 3
        WHEN se.severity = 'low' THEN 4
        ELSE 5
    END as severity_order
FROM public.security_events se
ORDER BY se.timestamp DESC;

-- Add RLS policy for site admins only
ALTER VIEW public.security_dashboard_view ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Site admins can view security dashboard" ON public.security_dashboard_view
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_site_admin = true
        )
    );

-- 2. Recreate public_profile_info without SECURITY DEFINER
DROP VIEW IF EXISTS public.public_profile_info CASCADE;

CREATE VIEW public.public_profile_info AS
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.email,
    p.role,
    p.organization_id,
    o.name as organization_name
FROM public.profiles p
LEFT JOIN public.organizations o ON p.organization_id = o.id;

-- Add RLS policy for same organization only
ALTER VIEW public.public_profile_info ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view profiles in their organization" ON public.public_profile_info
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM public.profiles 
            WHERE id = auth.uid()
        )
    );

-- 3. Recreate public_tasks without SECURITY DEFINER
DROP VIEW IF EXISTS public.public_tasks CASCADE;

CREATE VIEW public.public_tasks AS
SELECT 
    t.id,
    t.title,
    t.description,
    t.task_type,
    t.priority,
    t.status,
    t.organization_id,
    t.created_at,
    p.first_name || ' ' || p.last_name as created_by_name
FROM public.tasks t
LEFT JOIN public.profiles p ON t.created_by = p.id;

-- Add RLS policy for same organization only
ALTER VIEW public.public_tasks ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view tasks in their organization" ON public.public_tasks
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM public.profiles 
            WHERE id = auth.uid()
        )
    );

-- 4. Recreate public_profiles without SECURITY DEFINER
DROP VIEW IF EXISTS public.public_profiles CASCADE;

CREATE VIEW public.public_profiles AS
SELECT 
    p.id,
    p.first_name,
    p.last_name,
    p.role,
    p.organization_id,
    p.created_at,
    o.name as organization_name,
    o.type as organization_type
FROM public.profiles p
LEFT JOIN public.organizations o ON p.organization_id = o.id;

-- Add RLS policy for same organization only
ALTER VIEW public.public_profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view profiles in their organization" ON public.public_profiles
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM public.profiles 
            WHERE id = auth.uid()
        )
    );

-- ============================================================================
-- GRANT PERMISSIONS
-- ============================================================================

-- Grant SELECT permissions to authenticated users
GRANT SELECT ON public.security_dashboard_view TO authenticated;
GRANT SELECT ON public.public_profile_info TO authenticated;
GRANT SELECT ON public.public_tasks TO authenticated;
GRANT SELECT ON public.public_profiles TO authenticated;

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Check that all views now have RLS enabled
SELECT 
    schemaname,
    viewname,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = v.viewname) as policy_count
FROM pg_views v
WHERE schemaname = 'public' 
AND viewname IN ('security_dashboard_view', 'public_profile_info', 'public_tasks', 'public_profiles')
ORDER BY viewname;

COMMIT;

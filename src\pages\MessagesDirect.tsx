import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Link, Navigate } from 'react-router-dom';

// IMPORTANT: This component is deprecated and should not be used.
// All chat functionality should use GetStream implementation via the Dashboard messages tab.
// This component is kept for backward compatibility but redirects to the new implementation.

interface TaskWithMessages {
  id: string;
  title: string;
  user_id: string;
}

const MessagesDirect = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('[MessagesDirect] This component is deprecated. Redirecting to Dashboard messages tab.');
  }
  // Simply redirect to the Dashboard messages tab
  return <Navigate to="/dashboard?tab=messages" replace />;
};

export default MessagesDirect;

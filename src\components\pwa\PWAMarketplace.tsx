import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import FriendlyAccessMessage from '@/components/ui/FriendlyAccessMessage';
import {
  MapPin,
  List,
  Map,
  Calendar,
  Clock,
  PoundSterling,
  SlidersHorizontal,
  ArrowUpDown,
  ChevronRight,
  Briefcase
} from 'lucide-react';
import PWAMobileLayout from './PWAMobileLayout';
import PWATaskCard from './PWATaskCard';
import PWATaskFilter from './PWATaskFilter';
import PWATaskSort from './PWATaskSort';
import IsolatedGoogleMap from './IsolatedGoogleMap';
import { loadGoogleMapsApi } from '@/utils/pwa-google-maps';
import { format } from 'date-fns';

// Define the Task interface
interface Task {
  id: string;
  title: string;
  description: string;
  status: string;
  budget: number | string; // Handle both number and string formats
  due_date: string;
  created_at: string;
  location: string;
  category: string;
  location_lat?: number;
  location_lng?: number;
  building?: string;
  room?: string;
  visibility: string;
  user_id: string;
  organization_id?: string;
  type?: string;
  offers?: { count: number }; // Count of offers from the joined table
}

// Define sort options
type SortOption = 'newest' | 'price_high_low' | 'price_low_high' | 'due_date';

const PWAMarketplace: React.FC = () => {
  const navigate = useNavigate();
  const { user, isSupplier, profile, userRole } = useAuth();

  // Debug auth information
  if (process.env.NODE_ENV === 'development') {

    console.log('[PWAMarketplace] Auth Debug: completed');

    }
  const [tasks, setTasks] = useState<Task[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [view, setView] = useState<'list' | 'map'>('list');
  const [showFilters, setShowFilters] = useState(false);
  const [showSortOptions, setShowSortOptions] = useState(false);
  const [sortOption, setSortOption] = useState<SortOption>('newest');

  // Filter states
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [minBudget, setMinBudget] = useState<number | null>(null);
  const [maxBudget, setMaxBudget] = useState<number | null>(null);
  const [locationFilter, setLocationFilter] = useState<string>('');

  // Fetch public tasks
  const fetchPublicTasks = async () => {
    if (process.env.NODE_ENV === 'development') {
    console.log('[PWAMarketplace] Starting to fetch public tasks');
  }
    setLoading(true);
    setError(null);

    try {
      // Fetch tasks that are public and open
      // Note: RLS policy "Suppliers can view all public tasks" allows suppliers to see all public tasks
      // regardless of organization_id
      if (process.env.NODE_ENV === 'development') {
    console.log('[PWAMarketplace] Making Supabase query for public tasks');
  }
      // Use a more robust query that doesn't rely on specific task IDs
      // Include count of offers for each task
      // Only show tasks in early stages (open, interest, questions, offer)
      // Explicitly exclude tasks that are assigned, in progress, or in later stages
      const { data, error } = await supabase
        .from('tasks')
        .select(`
          *,
          offers:offers(count)
        `)
        .eq('visibility', 'public')
        .in('status', ['open', 'interest', 'questions', 'offer']) // Only include tasks in early stages
        .not('status', 'in', ['assigned', 'in_progress', 'pending_payment', 'completed', 'closed', 'cancelled']) // Explicitly exclude later stages
        .order('created_at', { ascending: false });

      if (process.env.NODE_ENV === 'development') {
    console.log('[PWAMarketplace] Raw query response: completed');
  }
      if (error) {
        console.error('[PWAMarketplace] Supabase error:', error);
        throw error;
      }

      if (process.env.NODE_ENV === 'development') {
    console.log('[PWAMarketplace] Fetched public tasks: completed');
  }
      if (data && data.length > 0) {
        if (process.env.NODE_ENV === 'development') {
    console.log('[PWAMarketplace] First task: completed');
  }
      } else {
        if (process.env.NODE_ENV === 'development') {
    console.log('[PWAMarketplace] No tasks found');
  }
      }

      setTasks(data || []);
      applyFiltersAndSort(data || [], categoryFilter, minBudget, maxBudget, locationFilter, sortOption);
    } catch (error: any) {
      console.error('[PWAMarketplace] Error fetching public tasks:', error);
      setError(error.message || 'Failed to load tasks');
    } finally {
      setLoading(false);
    }
  };

  // Apply filters and sorting
  const applyFiltersAndSort = (
    tasksToFilter: Task[],
    category: string,
    min: number | null,
    max: number | null,
    location: string,
    sort: SortOption
  ) => {
    // Apply filters
    let result = [...tasksToFilter];

    // Category filter
    if (category !== 'all') {
      result = result.filter(task => task.category === category);
    }

    // Budget filters
    if (min !== null) {
      result = result.filter(task => {
        const budget = typeof task.budget === 'number' ? task.budget : parseFloat(task.budget as string);
        return budget >= min;
      });
    }

    if (max !== null) {
      result = result.filter(task => {
        const budget = typeof task.budget === 'number' ? task.budget : parseFloat(task.budget as string);
        return budget <= max;
      });
    }

    // Location filter
    if (location) {
      const locationLower = location.toLowerCase();
      result = result.filter(task =>
        task.location.toLowerCase().includes(locationLower) ||
        (task.building && task.building.toLowerCase().includes(locationLower))
      );
    }

    // Apply sorting
    switch (sort) {
      case 'newest':
        result.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
      case 'price_high_low':
        result.sort((a, b) => {
          const budgetA = typeof a.budget === 'number' ? a.budget : parseFloat(a.budget as string);
          const budgetB = typeof b.budget === 'number' ? b.budget : parseFloat(b.budget as string);
          return budgetB - budgetA;
        });
        break;
      case 'price_low_high':
        result.sort((a, b) => {
          const budgetA = typeof a.budget === 'number' ? a.budget : parseFloat(a.budget as string);
          const budgetB = typeof b.budget === 'number' ? b.budget : parseFloat(b.budget as string);
          return budgetA - budgetB;
        });
        break;
      case 'due_date':
        result.sort((a, b) => new Date(a.due_date).getTime() - new Date(b.due_date).getTime());
        break;
    }

    setFilteredTasks(result);
  };

  // Handle filter changes
  const handleFilterChange = () => {
    applyFiltersAndSort(tasks, categoryFilter, minBudget, maxBudget, locationFilter, sortOption);
    setShowFilters(false);
  };

  // Handle sort change
  const handleSortChange = (option: SortOption) => {
    setSortOption(option);
    applyFiltersAndSort(tasks, categoryFilter, minBudget, maxBudget, locationFilter, option);
    setShowSortOptions(false);
  };

  // Initial data fetch
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {

      console.log('[PWAMarketplace] User: completed');


      }
    if (user && isSupplier) {
      if (process.env.NODE_ENV === 'development') {
    console.log('[PWAMarketplace] Fetching public tasks for supplier');
  }
      fetchPublicTasks();

      // Preload Google Maps API for faster map view switching
      loadGoogleMapsApi(() => {
        if (process.env.NODE_ENV === 'development') {
    console.log('[PWAMarketplace] Google Maps API preloaded successfully');
  }
      });
    } else {
      if (process.env.NODE_ENV === 'development') {

        console.log('[PWAMarketplace] Not fetching tasks - user or supplier role missing');

        }
    }
  }, [user, isSupplier]);

  // Show friendly message for non-suppliers
  if (!isSupplier) {
    return (
      <PWAMobileLayout>
        <FriendlyAccessMessage
          title="Marketplace Information"
          message="The marketplace is where professional suppliers can view and bid on tasks that need external support."
          howItWorks="When your school posts tasks that need external help, they appear here for qualified professionals to express interest and submit offers."
          additionalInfo="While you can't view the marketplace directly, any tasks from your school that need external support will be posted here for suppliers to find and respond to."
          icon={Briefcase}
          iconColorClass="text-blue-500"
          iconBgClass="bg-blue-50"
          primaryButtonText="Go to My Tasks"
          primaryButtonPath="/tasks"
        />
      </PWAMobileLayout>
    );
  }

  return (
    <PWAMobileLayout>
      <div className="container max-w-md mx-auto px-4 py-4">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-xl font-semibold">Marketplace</h1>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => setShowFilters(true)}
            >
              <SlidersHorizontal className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => setShowSortOptions(true)}
            >
              <ArrowUpDown className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* View toggle */}
        <Tabs
          value={view}
          onValueChange={(value) => setView(value as 'list' | 'map')}
          className="mb-4"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger
              value="list"
              className="flex items-center"
            >
              <List className="h-4 w-4 mr-2" /> List View
            </TabsTrigger>
            <TabsTrigger
              value="map"
              className="flex items-center"
            >
              <Map className="h-4 w-4 mr-2" /> Map View
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Active filters display */}
        {(categoryFilter !== 'all' || minBudget !== null || maxBudget !== null || locationFilter) && (
          <div className="flex flex-wrap gap-2 mb-4">
            {categoryFilter !== 'all' && (
              <Badge variant="secondary" className="flex items-center">
                Category: {categoryFilter}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 ml-1"
                  onClick={() => {
                    setCategoryFilter('all');
                    applyFiltersAndSort(tasks, 'all', minBudget, maxBudget, locationFilter, sortOption);
                  }}
                >
                  ×
                </Button>
              </Badge>
            )}
            {(minBudget !== null || maxBudget !== null) && (
              <Badge variant="secondary" className="flex items-center">
                Budget: {minBudget !== null ? `£${minBudget}` : '£0'} - {maxBudget !== null ? `£${maxBudget}` : 'Any'}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 ml-1"
                  onClick={() => {
                    setMinBudget(null);
                    setMaxBudget(null);
                    applyFiltersAndSort(tasks, categoryFilter, null, null, locationFilter, sortOption);
                  }}
                >
                  ×
                </Button>
              </Badge>
            )}
            {locationFilter && (
              <Badge variant="secondary" className="flex items-center">
                Location: {locationFilter}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 ml-1"
                  onClick={() => {
                    setLocationFilter('');
                    applyFiltersAndSort(tasks, categoryFilter, minBudget, maxBudget, '', sortOption);
                  }}
                >
                  ×
                </Button>
              </Badge>
            )}
          </div>
        )}

        {/* Sort indicator */}
        <div className="mb-4 text-sm text-gray-500 flex items-center">
          <ArrowUpDown className="h-3 w-3 mr-1" />
          Sorted by: {' '}
          <span className="font-medium ml-1">
            {sortOption === 'newest' ? 'Newest first' :
             sortOption === 'price_high_low' ? 'Price: High to low' :
             sortOption === 'price_low_high' ? 'Price: Low to high' :
             'Due date: Soonest first'}
          </span>
        </div>

        {/* Error state */}
        {error && (
          <Card className="mb-4">
            <CardContent className="p-4 text-center">
              <p className="text-red-500">{error}</p>
              <Button
                variant="outline"
                className="mt-2"
                onClick={fetchPublicTasks}
              >
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Loading state */}
        {loading && (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-32 w-full" />
            ))}
          </div>
        )}

        {/* List view */}
        {view === 'list' && !loading && (
          <div className="space-y-4">
            {filteredTasks.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <p className="text-gray-500">No tasks found matching your criteria.</p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => {
                      setCategoryFilter('all');
                      setMinBudget(null);
                      setMaxBudget(null);
                      setLocationFilter('');
                      applyFiltersAndSort(tasks, 'all', null, null, '', sortOption);
                    }}
                  >
                    Clear Filters
                  </Button>
                </CardContent>
              </Card>
            ) : (
              filteredTasks.map(task => (
                <Card
                  key={task.id}
                  className="mb-2 border-l-4 shadow-sm cursor-pointer hover:bg-gray-50 transition-colors"
                  style={{
                    borderLeftColor: '#22c55e' // Always green for marketplace tasks
                  }}
                  onClick={() => navigate(`/tasks/${task.id}`)}
                >
                  <CardContent className="p-3">
                    <div className="flex justify-between items-start">
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-sm truncate">{task.title}</h3>
                        <div className="flex items-center text-xs text-gray-500 mt-1">
                          <MapPin size={12} className="mr-1 flex-shrink-0" />
                          <span className="truncate">{task.location}</span>
                        </div>
                        <div className="flex items-center text-xs text-gray-500 mt-1">
                          <Calendar size={12} className="mr-1 flex-shrink-0" />
                          {format(new Date(task.due_date), 'dd MMM yyyy')}
                        </div>
                        <div className="flex items-center text-xs text-gray-500 mt-1">
                          <span className="font-medium">{task.offers?.count || task.offers_count || 0}</span>
                          <span className="ml-1">{(task.offers?.count === 1 || task.offers_count === 1) ? 'offer' : 'offers'} received</span>
                        </div>
                      </div>
                      <div className="flex flex-col items-end ml-2">
                        <Badge
                          variant="outline"
                          className="text-xs px-2 py-0.5 bg-green-100 text-green-800 border-green-200"
                        >
                          {task.category}
                        </Badge>
                        <div className="flex items-center text-xs font-medium mt-1">
                          <PoundSterling size={12} className="mr-0.5" />
                          {typeof task.budget === 'number'
                            ? task.budget.toFixed(2)
                            : parseFloat(task.budget as string).toFixed(2)}
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-end mt-1">
                      <ChevronRight className="h-4 w-4 text-gray-400" />
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )}

        {/* Map view - using isolated map component to prevent DOM manipulation errors */}
        <div style={{ display: view === 'map' && !loading ? 'block' : 'none' }}>
          <IsolatedGoogleMap
            tasks={filteredTasks}
            visible={view === 'map' && !loading}
            onTaskSelect={(taskId) => navigate(`/tasks/${taskId}`)}
          />
        </div>

        {/* Filter Sheet */}
        <PWATaskFilter
          open={showFilters}
          onOpenChange={setShowFilters}
          categoryFilter={categoryFilter}
          setCategoryFilter={setCategoryFilter}
          minBudget={minBudget}
          setMinBudget={setMinBudget}
          maxBudget={maxBudget}
          setMaxBudget={setMaxBudget}
          locationFilter={locationFilter}
          setLocationFilter={setLocationFilter}
          onApplyFilters={handleFilterChange}
        />

        {/* Sort Sheet */}
        <PWATaskSort
          open={showSortOptions}
          onOpenChange={setShowSortOptions}
          sortOption={sortOption}
          onSortChange={handleSortChange}
        />
      </div>
    </PWAMobileLayout>
  );
};

export default PWAMarketplace;

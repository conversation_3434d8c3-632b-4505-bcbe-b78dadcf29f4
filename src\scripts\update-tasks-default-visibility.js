// Script to update the default visibility for the tasks table
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateTasksDefaultVisibility() {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log('Updating tasks table default visibility...');
  }
    // First, check the current default value
    const { data: columnInfo, error: infoError } = await supabase
      .from('tasks')
      .select('visibility')
      .limit(1);
    
    if (infoError) {
      console.error('Error checking tasks table:', infoError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log('Sample task visibility: completed');
  }
    // Update the default value for the visibility column to 'admin'
    const { error: updateError } = await supabase.rpc('alter_column_set_default', {
      table_name: 'tasks',
      column_name: 'visibility',
      default_value: "'admin'"
    });
    
    if (updateError) {
      console.error('Error updating default value:', updateError);
      if (process.env.NODE_ENV === 'development') {
    console.log('Trying alternative approach...');
  }
      // Try using raw SQL if the RPC method fails
      const { error: sqlError } = await supabase.rpc('execute_sql', {
        sql: `ALTER TABLE public.tasks ALTER COLUMN visibility SET DEFAULT 'admin';`
      });
      
      if (sqlError) {
        console.error('Error executing SQL:', sqlError);
        return;
      }
      
      if (process.env.NODE_ENV === 'development') {
    console.log('Successfully updated default value using SQL');
  }
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Successfully updated default value using RPC');
  }
    }
    
    // Verify the change
    if (process.env.NODE_ENV === 'development') {
    console.log('Verifying the change...');
  }
    // Create a test task without specifying visibility to check the default
    const testTask = {
      title: 'Test Default Visibility',
      description: 'This task is created to test the default visibility value',
      location: 'Test Location',
      category: 'Test Category',
      budget: 100,
      due_date: new Date().toISOString(),
      user_id: '4a82d673-eee5-4ddf-b455-395ce3a73459' // Teacher user ID
    };
    
    // We won't actually create the test task, as we've already fixed the code
    // This is just to show what would happen
    if (process.env.NODE_ENV === 'development') {
    console.log('If we created a task now without specifying visibility, it would use the default value "admin"');
  }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

updateTasksDefaultVisibility();


import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useOffers } from '@/hooks/use-offers';
import {
  Loader2,
  AlertCircle,
  CheckCircle,
  PoundSterling,
  MessageSquare,
  CheckSquare,
  PlayCircle,
  ArrowRight,
  Clock,
  CheckCheck,
  MessageCircle,
  RefreshCw
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Offer, Task } from '@/services/taskService';
import { supabase } from '@/integrations/supabase/client';
import { useQueryClient } from '@tanstack/react-query';
import systemMessageService from '@/services/systemMessageService';
import streamSystemMessages from '@/utils/streamSystemMessages';
import { getStreamChannelForTask } from '@/utils/getStreamChannel';
import { getTaskChannel, createOrUpdateTaskChannel } from '@/integrations/getstream/client';

// Helper function to normalize status display (handles both "pending" and "awaiting")
const normalizeStatus = (status: string): string => {
  if (status === 'pending') return 'awaiting';
  return status;
};

// Progress indicator component for interest expressed
const InterestProgressIndicator = ({
  task
}: {
  task: Task
}) => {
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-blue-700 font-medium">Discussion Started</h3>
        <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
          Active
        </Badge>
      </div>
      <p className="text-sm text-gray-600 mb-3">
        You've expressed interest in this task and started a conversation
      </p>

      <div className="flex items-center gap-2 text-sm bg-white p-3 rounded-md border border-blue-100">
        <div className="bg-blue-100 text-blue-800 p-2 rounded-full">
          <MessageCircle size={16} />
        </div>
        <div className="flex-1">
          <p className="font-medium">Chat is active</p>
          <p className="text-gray-500">Continue the conversation in the chat section below</p>
        </div>
      </div>

      <div className="mt-3 text-sm">
        <p className="font-medium text-gray-700">Ready to make an offer?</p>
        <p className="text-gray-500">
          Once you've discussed the details, you can submit a formal offer below
        </p>
      </div>
    </div>
  );
};

// Progress indicator component for offer submitted
const OfferProgressIndicator = ({
  offer
}: {
  offer: Offer
}) => {
  const status = normalizeStatus(offer.status);

  // Determine background color based on status
  const bgColor = status === 'awaiting'
    ? 'bg-yellow-50 border-yellow-200'
    : status === 'accepted'
      ? 'bg-green-50 border-green-200'
      : 'bg-red-50 border-red-200';

  // Determine heading color based on status
  const headingColor = status === 'awaiting'
    ? 'text-yellow-700'
    : status === 'accepted'
      ? 'text-green-700'
      : 'text-red-700';

  return (
    <div className={`${bgColor} border rounded-lg p-4 mb-4`}>
      <div className="flex items-center justify-between mb-2">
        <h3 className={`${headingColor} font-medium`}>Offer Submitted</h3>
        <Badge
          variant="outline"
          className={
            status === 'awaiting'
              ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
              : status === 'accepted'
                ? 'bg-green-100 text-green-800 border-green-200'
                : 'bg-red-100 text-red-800 border-red-200'
          }
        >
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </Badge>
      </div>
      <p className="text-sm text-gray-600 mb-3">
        Your formal offer has been submitted to the school
      </p>

      {/* Offer details */}
      <div className="bg-white p-3 rounded-md border border-gray-200 space-y-2 mb-3">
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">Amount:</span>
          <span className="font-medium">£{Number(offer.amount).toFixed(2)}</span>
        </div>
        <div>
          <span className="text-sm text-gray-600">Message:</span>
          <p className="text-sm mt-1 bg-gray-50 p-2 rounded border border-gray-100">{offer.message}</p>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-600">Submitted:</span>
          <span className="text-sm">{new Date(offer.created_at).toLocaleDateString()}</span>
        </div>
      </div>

      {/* Status-specific message */}
      {status === 'awaiting' && (
        <div className="flex items-center gap-2 text-sm bg-white p-3 rounded-md border border-yellow-100">
          <div className="bg-yellow-100 text-yellow-800 p-2 rounded-full">
            <Clock size={16} />
          </div>
          <div className="flex-1">
            <p className="font-medium">Waiting for response</p>
            <p className="text-gray-500">The school is reviewing your offer</p>
          </div>
        </div>
      )}

      {status === 'accepted' && (
        <div className="flex items-center gap-2 text-sm bg-white p-3 rounded-md border border-green-100">
          <div className="bg-green-100 text-green-800 p-2 rounded-full">
            <CheckCheck size={16} />
          </div>
          <div className="flex-1">
            <p className="font-medium">Offer accepted!</p>
            <p className="text-gray-500">You can now start working on this task</p>
          </div>
        </div>
      )}

      {status === 'rejected' && (
        <div className="flex items-center gap-2 text-sm bg-white p-3 rounded-md border border-red-100">
          <div className="bg-red-100 text-red-800 p-2 rounded-full">
            <AlertCircle size={16} />
          </div>
          <div className="flex-1">
            <p className="font-medium">Offer rejected</p>
            <p className="text-gray-500">You can submit a new offer or discuss further</p>
          </div>
        </div>
      )}
    </div>
  );
};

interface SupplierActionsProps {
  task: Task;
  existingOffer?: Offer;
  showRequestInfo?: boolean;
  onRequestInfo: () => void;
}

const SupplierActions = ({
  task,
  existingOffer,
  showRequestInfo = false,
  onRequestInfo
}: SupplierActionsProps) => {
  if (process.env.NODE_ENV === 'development') {
    console.log("SupplierActions rendered with task:", task.id);
  }
  if (process.env.NODE_ENV === 'development') {
    console.log("Existing offer:", existingOffer);
  }
  const [offerAmount, setOfferAmount] = useState(task?.budget?.toString() || '');
  const [offerMessage, setOfferMessage] = useState('');
  const [showCounterOffer, setShowCounterOffer] = useState(false);
  const [isMarkingCompleted, setIsMarkingCompleted] = useState(false);
  const [hasExpressedInterest, setHasExpressedInterest] = useState(false);
  const [hasSubmittedOffer, setHasSubmittedOffer] = useState(false);
  const { user, isSupplier } = useAuth();
  const { toast } = useToast();
  const { createOffer, isCreatingOffer } = useOffers();
  const queryClient = useQueryClient();

  // Check if the user has already expressed interest or submitted an offer
  useEffect(() => {
    if (!user) return;

    const checkExistingInterest = async () => {
      try {
        // Check for existing GetStream channel (indicates expressed interest)
        try {
          const channel = await getTaskChannel(task.id);

          // Check if the current user is a member of the channel
          if (channel && channel.state && channel.state.members) {
            const members = Object.keys(channel.state.members);
            if (members.includes(user.id)) {
              if (process.env.NODE_ENV === 'development') {

                console.log("User has already expressed interest in this task via GetStream channel");

                }
              setHasExpressedInterest(true);
            }
          }
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
    console.log("No existing GetStream channel found for this task");
  }
        }

        // If there's an existing offer, set hasSubmittedOffer to true
        if (existingOffer) {
          if (process.env.NODE_ENV === 'development') {

            console.log("User has already submitted an offer for this task:", existingOffer);

            }
          setHasSubmittedOffer(true);
        }
      } catch (error) {
        console.error("Error checking existing interest:", error);
      }
    };

    checkExistingInterest();
  }, [user, task.id, existingOffer]);

  const handleExpressInterest = async () => {
    if (process.env.NODE_ENV === 'development') {
    console.log("SupplierActions.handleExpressInterest called");
  }
    if (!user || !isSupplier) {
      console.warn("Cannot express interest - user not logged in or not a supplier", {
        loggedIn: !!user,
        isSupplier
      });
      return;
    }

    try {
      // Check if a GetStream channel already exists for this task
      let channelExists = false;
      let channel;

      try {
        channel = await getTaskChannel(task.id);

        // Check if the current user is a member of the channel
        const members = Object.keys(channel.state?.members || {});
        channelExists = members.includes(user.id);

        if (process.env.NODE_ENV === 'development') {


          console.log("GetStream channel check:", {
          channelExists,
          members,
          userId: user.id
        });


          }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
    console.log("No existing GetStream channel found, will create a new one");
  }
        channelExists = false;
      }

      // If a channel already exists and the user is a member, just open the chat
      if (channelExists) {
        if (process.env.NODE_ENV === 'development') {

          console.log("Channel already exists and user is a member");

          }
        toast({
          title: "Chat already open",
          description: "You've already expressed interest in this task. Continue the conversation in the chat.",
        });

        // Navigate to the task page with the chat open
        window.location.href = `/tasks/enhanced/${task.id}?messages=true`;
        return;
      }

      // Create a new GetStream channel or add the user to the existing one
      try {
        if (process.env.NODE_ENV === 'development') {
    console.log("Creating or updating GetStream channel for task:", task.id);
  }
        // Create the channel with the task creator and the supplier
        channel = await createOrUpdateTaskChannel(
          task.id,
          task.title,
          [user.id, task.user_id],
          true
        );

        // Send an initial message
        const initialMessage = `Hi, I'm interested in this task and would like to discuss the requirements.`;

        await channel.sendMessage({
          text: initialMessage,
          user_id: user.id
        });

        // Also send a system message
        await channel.sendMessage({
          text: `${user.email || 'A supplier'} has expressed interest in this task.`,
          user_id: user.id,
          type: 'system',
        });

        if (process.env.NODE_ENV === 'development') {
    console.log("Successfully created/updated GetStream channel for task:", task.id);
  }
      } catch (error) {
        console.error("Error creating/updating GetStream channel:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "There was a problem expressing interest. Please try again.",
        });
        return;
      }

      // Add a system message about expressing interest using GetStream
      try {
        // We already have the channel from earlier in this function
        if (process.env.NODE_ENV === 'development') {
    console.log('Using GetStream for system message');
  }
        // Send a status change message
        await streamSystemMessages.createStatusChangeStreamMessage(
          task.id,
          'interest',
          channel,
          undefined,
          false // Not an internal task
        );
      } catch (error) {
        console.error('Error sending system message:', error);
        // Continue anyway, the interest was expressed successfully
      }

      // Show success message
      toast({
        title: "Interest expressed",
        description: "You've expressed interest in this task. Continue the conversation in the chat.",
      });

      // Update state to show the interest has been expressed
      setHasExpressedInterest(true);
    } catch (error) {
      console.error("Error expressing interest:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "There was a problem expressing interest. Please try again.",
      });
    }
  };

  const handleSubmitCounterOffer = async (e: React.FormEvent) => {
    e.preventDefault();
    if (process.env.NODE_ENV === 'development') {
    console.log("SupplierActions.handleSubmitCounterOffer called");
  }
    if (!user || !isSupplier) {
      console.warn("Cannot submit offer - user not logged in or not a supplier", {
        loggedIn: !!user,
        isSupplier
      });
      return;
    }

    if (!offerAmount || !offerMessage) {
      console.warn("Missing required fields for offer");
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please provide both an amount and a message for your offer.",
      });
      return;
    }

    if (process.env.NODE_ENV === 'development') {
    console.log("Submitting formal offer:", {
      taskId: task.id,
      amount: offerAmount,
      message: offerMessage
    });
  }
    try {
      // Create the offer
      createOffer({
        task_id: task.id,
        amount: parseFloat(offerAmount),
        message: offerMessage,
      });

      // Get or create the GetStream channel
      try {
        // Try to get the existing channel
        let channel;

        try {
          channel = await getTaskChannel(task.id);
          if (process.env.NODE_ENV === 'development') {
    console.log('Found existing GetStream channel for task: completed');
  }
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
    console.log('No existing GetStream channel found, creating a new one');
  }
          // Create a new channel
          channel = await createOrUpdateTaskChannel(
            task.id,
            task.title,
            [user.id, task.user_id],
            true
          );
        }

        // Add a system message to the channel about the offer
        const systemMessage = `${user.email} has submitted a formal offer of £${parseFloat(offerAmount).toFixed(2)}.`;

        // Send the offer message
        await streamSystemMessages.sendStreamSystemMessage(channel, systemMessage);

        // Also add a system message about moving to questions phase
        await streamSystemMessages.createStatusChangeStreamMessage(
          task.id,
          'questions',
          channel,
          undefined,
          false // Not an internal task
        );

        if (process.env.NODE_ENV === 'development') {
    console.log('Successfully sent offer system messages via GetStream');
  }
      } catch (error) {
        console.error('Error sending offer system messages via GetStream:', error);
        // Continue anyway, the offer was created successfully
      }

      // Show success message
      toast({
        title: "Offer submitted",
        description: "Your formal offer has been submitted successfully.",
      });

      // Update state to show the offer has been submitted
      setHasSubmittedOffer(true);

      // Refresh the task data to get the updated offer
      queryClient.invalidateQueries({ queryKey: ['task', task.id] });
      queryClient.invalidateQueries({ queryKey: ['offers', task.id] });

      // Reset form and close popover
      setOfferAmount(task?.budget?.toString() || '');
      setOfferMessage('');
      setShowCounterOffer(false);
    } catch (error) {
      console.error("Error submitting offer:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "There was a problem submitting your offer. Please try again.",
      });
    }
  };

  // Function to mark a task as completed by the supplier
  const handleMarkCompleted = async () => {
    if (!user || !isSupplier) {
      console.warn("Cannot mark task as completed - user not logged in or not a supplier", {
        loggedIn: !!user,
        isSupplier
      });
      return;
    }

    try {
      setIsMarkingCompleted(true);

      if (process.env.NODE_ENV === 'development') {
    console.log("Marking task as completed:", task.id);
  }
      // Update the task status to completed
      const { error } = await supabase
        .from('tasks')
        .update({
          status: 'completed',
          updated_at: new Date().toISOString()
        })
        .eq('id', task.id);

      if (error) {
        console.error("Error marking task as completed:", error);
        throw error;
      }

      // Show success message
      toast({
        title: "Task marked as completed",
        description: "The task has been marked as completed and is awaiting admin approval.",
        variant: "default",
      });

      // Get active thread ID if available
      let activeThreadId: string | undefined;
      try {
        const { data: threadData } = await supabase
          .from('chat_threads')
          .select('id')
          .eq('task_id', task.id)
          .eq('supplier_id', user.id)
          .maybeSingle();

        activeThreadId = threadData?.id;
      } catch (error) {
        console.error('Error fetching thread ID:', error);
      }

      // Send system message about status change
      // First try to get the GetStream channel
      const channel = await getStreamChannelForTask(task.id);

      if (channel) {
        // If we have a GetStream channel, use the streamSystemMessages utility
        if (process.env.NODE_ENV === 'development') {
    console.log('Using GetStream for completed system message');
  }
        await streamSystemMessages.createStatusChangeStreamMessage(
          task.id,
          'completed',
          channel,
          undefined,
          false // Not an internal task
        );
      } else {
        // Fallback to the old method if channel not available
        if (process.env.NODE_ENV === 'development') {
    console.log('Fallback to legacy system message for completed');
  }
        await systemMessageService.createStatusChangeMessage(task.id, 'completed', undefined, activeThreadId);
      }

      // Refresh task data
      queryClient.invalidateQueries({ queryKey: ['task', task.id] });

    } catch (error) {
      console.error("Error marking task as completed:", error);
      toast({
        title: "Error",
        description: "Failed to mark task as completed. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsMarkingCompleted(false);
    }
  };

  // Only show for suppliers when task has public visibility AND is in a relevant status
  const hasOffers = existingOffer !== undefined;

  // First check if this is an external task (public visibility)
  const isExternalTask = task?.visibility === 'public';

  // Then check if the task is in a status where supplier actions are relevant
  const isRelevantStatus = task?.status === 'open' ||
                          task?.status === 'assigned' ||
                          task?.status === 'in_progress' ||
                          task?.status === 'questions' ||
                          task?.status === 'interest' ||
                          hasOffers;

  // Only show supplier actions for external tasks in relevant statuses
  const isTaskAvailableForOffers = isExternalTask && isRelevantStatus;

  if (process.env.NODE_ENV === 'development') {
    console.log("SupplierActions - Task status:", task?.status);
  }
  if (process.env.NODE_ENV === 'development') {
    console.log("SupplierActions - Task visibility:", task?.visibility);
  }
  if (process.env.NODE_ENV === 'development') {
    console.log("SupplierActions - isExternalTask:", isExternalTask);
  }
  if (process.env.NODE_ENV === 'development') {
    console.log("SupplierActions - isRelevantStatus:", isRelevantStatus);
  }
  if (process.env.NODE_ENV === 'development') {
    console.log("SupplierActions - isTaskAvailableForOffers:", isTaskAvailableForOffers);
  }
  if (!isSupplier || !isTaskAvailableForOffers) {
    if (process.env.NODE_ENV === 'development') {
    console.log("Not rendering SupplierActions - conditions not met:", {
      isSupplier,
      taskStatus: task?.status,
      taskVisibility: task?.visibility,
      isExternalTask,
      isRelevantStatus,
      isTaskAvailableForOffers
    });
  }
    return null;
  }

  // Get normalized status if offer exists
  const displayStatus = existingOffer ? normalizeStatus(existingOffer.status) : '';
  if (process.env.NODE_ENV === 'development') {
    console.log("Normalized offer status:", displayStatus);
  }
  // Function to mark a task as in progress by the supplier
  const handleStartTask = async () => {
    if (!user || !isSupplier) {
      console.warn("Cannot start task - user not logged in or not a supplier", {
        loggedIn: !!user,
        isSupplier
      });
      return;
    }

    try {
      setIsMarkingCompleted(true); // Reuse the same loading state

      if (process.env.NODE_ENV === 'development') {


        console.log("Starting task (marking as in_progress):", task.id);



        }
      // Update the task status to in_progress
      const { error } = await supabase
        .from('tasks')
        .update({
          status: 'in_progress',
          updated_at: new Date().toISOString()
        })
        .eq('id', task.id);

      if (error) {
        console.error("Error marking task as in progress:", error);
        throw error;
      }

      // Show success message
      toast({
        title: "Task started",
        description: "You have started work on this task. Mark it as completed when you're done.",
        variant: "default",
      });

      // Get active thread ID if available
      let activeThreadId: string | undefined;
      try {
        const { data: threadData } = await supabase
          .from('chat_threads')
          .select('id')
          .eq('task_id', task.id)
          .eq('supplier_id', user.id)
          .maybeSingle();

        activeThreadId = threadData?.id;
      } catch (error) {
        console.error('Error fetching thread ID:', error);
      }

      // Send system message about status change
      // First try to get the GetStream channel
      const channel = await getStreamChannelForTask(task.id);

      if (channel) {
        // If we have a GetStream channel, use the streamSystemMessages utility
        if (process.env.NODE_ENV === 'development') {
    console.log('Using GetStream for in_progress system message');
  }
        await streamSystemMessages.createStatusChangeStreamMessage(
          task.id,
          'in_progress',
          channel,
          undefined,
          false // Not an internal task
        );
      } else {
        // Fallback to the old method if channel not available
        if (process.env.NODE_ENV === 'development') {
    console.log('Fallback to legacy system message for in_progress');
  }
        await systemMessageService.createStatusChangeMessage(task.id, 'in_progress', undefined, activeThreadId);
      }

      // Refresh task data
      queryClient.invalidateQueries({ queryKey: ['task', task.id] });

    } catch (error) {
      console.error("Error starting task:", error);
      toast({
        title: "Error",
        description: "Failed to start task. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsMarkingCompleted(false);
    }
  };

  // Render the "Start Task" button for suppliers when the task is assigned to them
  const renderStartTaskButton = () => {
    if (task?.status === 'assigned' && isSupplier && user && task.assigned_to === user.id) {
      return (
        <Card className="mb-4">
          <CardHeader>
            <CardTitle>Start Task</CardTitle>
            <CardDescription>
              Click here to accept this assignment and start working on the task
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={handleStartTask}
              className="w-full bg-blue-600 hover:bg-blue-700"
              disabled={isMarkingCompleted}
            >
              {isMarkingCompleted ? (
                <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
              ) : (
                <><PlayCircle className="mr-2 h-4 w-4" /> Accept & Start Task</>
              )}
            </Button>
          </CardContent>
        </Card>
      );
    }
    return null;
  };

  // Render the "Mark as Completed" button for suppliers when the task is in progress
  const renderCompletionButton = () => {
    if (task?.status === 'in_progress' && isSupplier && user && task.assigned_to === user.id) {
      return (
        <Card className="mb-4">
          <CardHeader>
            <CardTitle>Complete Task</CardTitle>
            <CardDescription>
              Mark this task as completed when you have finished the work
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={handleMarkCompleted}
              className="w-full bg-green-600 hover:bg-green-700"
              disabled={isMarkingCompleted}
            >
              {isMarkingCompleted ? (
                <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
              ) : (
                <><CheckSquare className="mr-2 h-4 w-4" /> Mark as Completed</>
              )}
            </Button>
          </CardContent>
        </Card>
      );
    }
    return null;
  };

  // Function to handle opening the chat
  const handleOpenChat = () => {
    window.location.href = `/tasks/enhanced/${task.id}?messages=true`;
  };

  // Debug logging for component rendering (development only)
  if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {

      console.log('SupplierActions rendering check:', {
      taskId: task?.id,
      taskTitle: task?.title,
      taskStatus: task?.status,
      taskVisibility: task?.visibility,
      hasExistingOffer: !!existingOffer,
      offerStatus: existingOffer?.status,
      displayStatus,
      isCreatingOffer,
      showCounterOffer,
      hasExpressedInterest,
      hasSubmittedOffer,
      renderTime: new Date().toISOString()
    });

      }
  }

  return (
    <div id="supplier-actions" className="space-y-4">
      {/* Render start task button if task is assigned */}
      {renderStartTaskButton()}

      {/* Render completion button if task is in progress */}
      {renderCompletionButton()}

      <div className="bg-white border rounded-lg shadow-sm">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold">Supplier Actions</h2>
          <p className="text-sm text-gray-500">
            {existingOffer
              ? `Your offer status: ${displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1)}`
              : "Express interest or submit an offer for this task"}
          </p>
        </div>
        <div className="p-4">
          {/* Handle various states */}
          {!user && (
            <Alert variant="default" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Authentication Required</AlertTitle>
              <AlertDescription>
                You need to sign in to take actions on this task.
              </AlertDescription>
            </Alert>
          )}

          {/* Show the appropriate UI based on user actions */}
          {existingOffer ? (
            // If the user has submitted an offer, show the offer progress indicator
            <OfferProgressIndicator offer={existingOffer} />
          ) : hasExpressedInterest ? (
            // If the user has expressed interest but not submitted an offer yet
            <div className="space-y-4">
              {/* Show the interest progress indicator */}
              <InterestProgressIndicator task={task} />

              {/* Also show the option to submit an offer now */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h3 className="text-green-700 font-medium mb-2">Ready to Make an Offer?</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Submit a formal offer now that you've discussed the details
                </p>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      className="w-full bg-green-600 hover:bg-green-700"
                      disabled={isCreatingOffer}
                    >
                      <PoundSterling className="mr-2 h-4 w-4" /> Submit Formal Offer
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-80">
                    <form onSubmit={handleSubmitCounterOffer} className="space-y-4">
                      <h4 className="font-medium">Your Offer Details</h4>
                      <div>
                        <label htmlFor="amount" className="block text-sm font-medium mb-1">
                          Your Price (£)
                        </label>
                        <Input
                          id="amount"
                          type="number"
                          step="0.01"
                          min="1"
                          value={offerAmount}
                          onChange={(e) => setOfferAmount(e.target.value)}
                          className="w-full"
                          required
                        />
                      </div>

                      <div>
                        <label htmlFor="message" className="block text-sm font-medium mb-1">
                          Message
                        </label>
                        <Textarea
                          id="message"
                          value={offerMessage}
                          onChange={(e) => setOfferMessage(e.target.value)}
                          placeholder="Explain your price and what you can offer..."
                          className="w-full"
                          rows={3}
                          required
                        />
                      </div>

                      <Button
                        type="submit"
                        className="w-full bg-green-600 hover:bg-green-700"
                        disabled={isCreatingOffer}
                      >
                        {isCreatingOffer ? (
                          <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Submitting...</>
                        ) : (
                          <><CheckCircle className="mr-2 h-4 w-4" /> Submit Offer</>
                        )}
                      </Button>
                    </form>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          ) : hasSubmittedOffer && !existingOffer ? (
            // This is a fallback in case hasSubmittedOffer is true but existingOffer is not available yet
            // This can happen during the brief period after submission before the data is refreshed
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="text-yellow-700 font-medium mb-2">Offer Submitted</h3>
              <p className="text-sm text-gray-600 mb-3">
                Your offer has been submitted and is being processed
              </p>

              <div className="flex items-center gap-2 text-sm bg-white p-3 rounded-md border border-yellow-100 mb-3">
                <div className="bg-yellow-100 text-yellow-800 p-2 rounded-full">
                  <Clock size={16} />
                </div>
                <div className="flex-1">
                  <p className="font-medium">Processing your offer</p>
                  <p className="text-gray-500">Please refresh the page in a moment to see your offer status</p>
                </div>
              </div>

              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="w-full"
              >
                <RefreshCw className="mr-2 h-4 w-4" /> Refresh Page
              </Button>
            </div>
          ) : (
            // Initial options view - neither expressed interest nor submitted offer
            <div className="space-y-6">
              <div className="bg-gray-50 p-4 rounded-lg mb-4">
                <h3 className="font-medium text-lg mb-2">Choose Your Approach</h3>
                <p className="text-gray-600 mb-4">You have two options for responding to this task:</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Option 1: Express Interest */}
                <Card className="border-blue-200">
                  <CardHeader>
                    <CardTitle className="text-blue-700">Option 1: Discuss First</CardTitle>
                    <CardDescription>
                      Start a conversation to learn more about the task before making an offer
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button
                      onClick={handleExpressInterest}
                      className="w-full bg-blue-600 hover:bg-blue-700"
                      disabled={isCreatingOffer}
                    >
                      {isCreatingOffer ? (
                        <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
                      ) : (
                        <><MessageSquare className="mr-2 h-4 w-4" /> Express Interest & Chat</>
                      )}
                    </Button>
                  </CardContent>
                </Card>

                {/* Option 2: Submit Offer Directly */}
                <Card className="border-green-200">
                  <CardHeader>
                    <CardTitle className="text-green-700">Option 2: Make an Offer</CardTitle>
                    <CardDescription>
                      Submit a formal offer directly if you're ready to proceed
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          className="w-full bg-green-600 hover:bg-green-700"
                          disabled={isCreatingOffer}
                        >
                          <PoundSterling className="mr-2 h-4 w-4" /> Submit Formal Offer
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-80">
                        <form onSubmit={handleSubmitCounterOffer} className="space-y-4">
                          <h4 className="font-medium">Your Offer Details</h4>
                          <div>
                            <label htmlFor="amount" className="block text-sm font-medium mb-1">
                              Your Price (£)
                            </label>
                            <Input
                              id="amount"
                              type="number"
                              step="0.01"
                              min="1"
                              value={offerAmount}
                              onChange={(e) => setOfferAmount(e.target.value)}
                              className="w-full"
                              required
                            />
                          </div>

                          <div>
                            <label htmlFor="message" className="block text-sm font-medium mb-1">
                              Message
                            </label>
                            <Textarea
                              id="message"
                              value={offerMessage}
                              onChange={(e) => setOfferMessage(e.target.value)}
                              placeholder="Explain your price and what you can offer..."
                              className="w-full"
                              rows={3}
                              required
                            />
                          </div>

                          <Button
                            type="submit"
                            className="w-full bg-green-600 hover:bg-green-700"
                            disabled={isCreatingOffer}
                          >
                            {isCreatingOffer ? (
                              <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Submitting...</>
                            ) : (
                              <><CheckCircle className="mr-2 h-4 w-4" /> Submit Offer</>
                            )}
                          </Button>
                        </form>
                      </PopoverContent>
                    </Popover>
                  </CardContent>
                </Card>
              </div>

              {/* Request More Info Button - Now at the bottom as a fallback */}
              <div className="mt-4">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={onRequestInfo}
                  disabled={isCreatingOffer}
                >
                  <MessageSquare className="mr-2 h-4 w-4" /> Request more information
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SupplierActions;

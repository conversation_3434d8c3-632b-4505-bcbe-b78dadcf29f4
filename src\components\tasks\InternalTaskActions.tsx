import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, CheckCircle, ArrowRight, ThumbsUp, User, Clock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Task } from '@/services/taskService';
import { useAuth } from '@/contexts/AuthContext';
import notificationService from '@/services/notificationService';
import systemMessageService from '@/services/systemMessageService';
import streamSystemMessages from '@/utils/streamSystemMessages';
import { getStreamChannelForTask } from '@/utils/getStreamChannel';
import { useQueryClient } from '@tanstack/react-query';
import CancelTaskActions from './CancelTaskActions';

interface InternalTaskActionsProps {
  task: Task;
  onTaskUpdated: () => void;
}

const InternalTaskActions = ({ task, onTaskUpdated }: InternalTaskActionsProps) => {
  const { toast } = useToast();
  const { user, profile } = useAuth();
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const queryClient = useQueryClient();

  // Basic validation - don't render if no task or user
  if (!task || !user) {
    return null;
  }

  // Check if this is an internal task
  const isInternalTask = task.visibility === 'internal';

  // Check user roles
  const isMaintenance = profile?.role === 'maintenance';
  const isSupport = profile?.role === 'support';
  const isAssignableStaff = isMaintenance || isSupport;
  const isAdmin = profile?.role === 'admin';
  const isTaskOwner = task.user_id === user.id;
  const isAssignedStaff = String(task.assigned_to) === String(user.id);

  // Show for:
  // 1. Internal tasks viewed by admins OR task owners
  // 2. Internal tasks viewed by assigned maintenance/support staff
  // 3. Any task assigned to maintenance/support staff who is viewing it
  const shouldShowActions =
    (isInternalTask && (isAdmin || isTaskOwner)) ||
    (isInternalTask && isAssignableStaff && isAssignedStaff) ||
    (isAssignableStaff && isAssignedStaff);

  // Don't render if conditions aren't met
  if (!shouldShowActions) {
    return null;
  }

  // Function to update task status
  const updateTaskStatus = async (newStatus: 'in_progress' | 'completed' | 'closed') => {
    try {
      setIsUpdatingStatus(true);

      // Update the task status
      const { error } = await supabase
        .from('tasks')
        .update({
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', task.id);

      if (error) {
        throw error;
      }

      // Create notification for task update
      if (user) {
        // Determine who to notify based on the status change
        let recipientId = '';
        let action: 'created' | 'updated' | 'completed' | 'assigned' | 'offer' = 'updated';

        if (newStatus === 'in_progress') {
          // Notify the task creator that work has started
          recipientId = task.user_id;
          action = 'updated'; // Map 'started' to 'updated' for notification service
        } else if (newStatus === 'completed') {
          // Notify the task creator that work is completed
          recipientId = task.user_id;
          action = 'completed'; // This matches the allowed values
        } else if (newStatus === 'closed') {
          // Notify the assigned staff that task is closed
          recipientId = task.assigned_to || '';
          action = 'completed'; // Map 'closed' to 'completed' for notification service
        }

        if (recipientId && recipientId !== user.id) {
          notificationService.createTaskUpdateNotification(
            recipientId,
            task.id,
            task.title,
            action,
            true // Send email
          );
        }
      }

      // Show success message
      toast({
        title: `Task ${newStatus.replace('_', ' ')}`,
        description: getStatusUpdateMessage(newStatus),
        variant: "default",
      });

      // Send system message to the chat thread
      try {
        // Get the chat thread ID for this task and the assigned staff
        const { data: threadData } = await supabase
          .from('chat_threads')
          .select('id')
          .eq('task_id', task.id)
          .eq('supplier_id', task.assigned_to || '')
          .maybeSingle();

        const threadId = threadData?.id;

        // First try to get the GetStream channel
        const channel = await getStreamChannelForTask(task.id);

        if (channel) {
          // If we have a GetStream channel, use the streamSystemMessages utility
          if (process.env.NODE_ENV === 'development') {

            console.log(`Using GetStream for ${newStatus} system message (internal task)`);

            }
          await streamSystemMessages.createStatusChangeStreamMessage(
            task.id,
            newStatus,
            channel,
            task.assigned_to,
            true // This is an internal task
          );
        } else if (threadId) {
          // Fallback to the old method if channel not available
          if (process.env.NODE_ENV === 'development') {

            console.log(`Fallback to legacy system message for ${newStatus} (internal task)`);

            }
          await systemMessageService.createStatusChangeMessage(
            task.id,
            newStatus,
            task.assigned_to,
            threadId,
            true // This is an internal task
          );
        }
      } catch (error) {
        console.error('Error creating system message for status change:', error);
      }

      // Refresh task data
      queryClient.invalidateQueries({ queryKey: ['task', task.id] });
      onTaskUpdated();
    } catch (error) {
      console.error(`Error updating task status to ${newStatus}:`, error);
      toast({
        title: "Error",
        description: `Failed to update task status. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Helper function to get status update messages
  const getStatusUpdateMessage = (status: string): string => {
    switch (status) {
      case 'in_progress':
        return "You've marked this task as in progress. The task creator will be notified.";
      case 'completed':
        return "You've marked this task as completed. The task creator will be notified to confirm completion.";
      case 'closed':
        return "You've closed this task. Thank you!";
      default:
        return "Task status has been updated.";
    }
  };

  // No longer needed - replaced by getNextAction

  // Determine the next action based on task status and user role
  const getNextAction = () => {
    // For assigned staff (they are responsible for starting and completing the task)
    if (isAssignedStaff) {
      if (task.status === 'assigned') {
        return {
          title: 'Start Work',
          description: 'Mark this task as in progress when you start working on it',
          action: () => updateTaskStatus('in_progress'),
          buttonText: 'Start Work',
          buttonIcon: <ArrowRight className="mr-2 h-4 w-4" />,
          buttonColor: 'bg-blue-600 hover:bg-blue-700'
        };
      } else if (task.status === 'in_progress') {
        return {
          title: 'Complete Task',
          description: 'Mark this task as completed when you have finished the work',
          action: () => updateTaskStatus('completed'),
          buttonText: 'Mark as Completed',
          buttonIcon: <CheckCircle className="mr-2 h-4 w-4" />,
          buttonColor: 'bg-green-600 hover:bg-green-700'
        };
      }
    }

    // For task owners or admins (they can only confirm completion)
    if (isTaskOwner || isAdmin) {
      if (task.status === 'completed') {
        return {
          title: 'Close Task',
          description: 'Close this task as completed satisfactorily',
          action: () => updateTaskStatus('closed'),
          buttonText: 'Close Task',
          buttonIcon: <ThumbsUp className="mr-2 h-4 w-4" />,
          buttonColor: 'bg-green-600 hover:bg-green-700'
        };
      } else if (task.status === 'assigned') {
        // Show a message that the assigned person needs to start the task
        return {
          title: 'Waiting for Staff',
          description: `This task is assigned to ${task.assigned_role} staff who needs to start the work`,
          infoOnly: true,
          infoText: `The assigned ${task.assigned_role} staff member needs to start this task`,
          infoIcon: <User className="mr-2 h-4 w-4" />,
          borderColor: 'border-yellow-300'
        };
      } else if (task.status === 'in_progress') {
        // Show a message that the assigned person needs to complete the task
        return {
          title: 'Work in Progress',
          description: `The ${task.assigned_role} staff is currently working on this task`,
          infoOnly: true,
          infoText: `Waiting for the ${task.assigned_role} staff to complete this task`,
          infoIcon: <Clock className="mr-2 h-4 w-4" />,
          borderColor: 'border-blue-300'
        };
      }
    }

    // No action available
    return null;
  };

  const nextAction = getNextAction();

  // If no action is available, return null
  if (!nextAction) {
    return (
      <div className="text-center p-4 text-gray-500">
        <p>No actions available for this task in its current state.</p>
        <p className="text-sm mt-2">Task status: {task.status}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4" id={`internal-task-actions-${task.id}`}>
      {nextAction.infoOnly ? (
        // Info-only display (no action button)
        <div className="p-4 rounded-md bg-gray-50 border border-gray-200">
          <div className="flex items-center text-gray-700 mb-2">
            {nextAction.infoIcon}
            <span className="font-medium">{nextAction.infoText}</span>
          </div>
          <p className="text-sm text-gray-600">
            {task.status === 'assigned'
              ? 'The assigned staff member will update the status when they start working on this task.'
              : 'The assigned staff member will mark this task as completed when the work is done.'}
          </p>
        </div>
      ) : (
        // Action button
        <Card className="mb-4">
          <CardHeader>
            <CardTitle>{nextAction.title}</CardTitle>
            <CardDescription>{nextAction.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={nextAction.action}
              className={`w-full ${nextAction.buttonColor}`}
              disabled={isUpdatingStatus}
            >
              {isUpdatingStatus ? (
                <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
              ) : (
                <>{nextAction.buttonIcon} {nextAction.buttonText}</>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Cancel task option for task owners and admins */}
      {(isTaskOwner || isAdmin) && (
        <CancelTaskActions
          task={task}
          onTaskUpdated={onTaskUpdated}
        />
      )}
    </div>
  );
};

export default InternalTaskActions;

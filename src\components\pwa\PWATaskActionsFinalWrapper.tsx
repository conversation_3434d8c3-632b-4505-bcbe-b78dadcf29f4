import React from 'react';
import { useParams } from 'react-router-dom';
import PWATaskActionsFinal from './PWATaskActionsFinal';
import PWAMobileLayout from './PWAMobileLayout';

/**
 * A wrapper component for PWATaskActionsFinal to ensure it's properly mounted
 * This helps isolate any issues with the PWATaskActions component
 */
const PWATaskActionsFinalWrapper: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  
  if (process.env.NODE_ENV === 'development') {
    console.log('[PWATaskActionsFinalWrapper] Rendering with task ID: completed');
  }
  return (
    <PWAMobileLayout>
      <PWATaskActionsFinal />
    </PWAMobileLayout>
  );
};

export default PWATaskActionsFinalWrapper;

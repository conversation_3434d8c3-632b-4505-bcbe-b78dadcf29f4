#!/usr/bin/env node

/**
 * Fix Malformed Console Logs from Automated Fixer
 * Cleans up the poorly formatted console.log statements
 */

const fs = require('fs');
const path = require('path');

// Colors for output
const colors = {
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

/**
 * Fix patterns for malformed console logs
 */
const MALFORMED_FIXES = [
  // Fix malformed development checks with console.log
  {
    pattern: /if \(process\.env\.NODE_ENV === 'development'\) \{\s*\n\s*\n\s*console\.log\(([^)]+)\);\s*\n\s*\n\s*\}/gm,
    replacement: 'if (process.env.NODE_ENV === \'development\') {\n    console.log($1);\n  }',
    name: 'Fix malformed development-wrapped console.log'
  },
  
  // Fix console.log with .replace(/user.*/, 'hasUser: ' + !!user) that got mangled
  {
    pattern: /console\.log\(([^)]+)\.replace\(\/user\.\*\/, 'hasUser: ' \+ !!user\)\);/g,
    replacement: 'console.log($1);',
    name: 'Remove mangled user replacement'
  },
  
  // Fix console.log statements that expose actual data
  {
    pattern: /console\.log\('([^']*)', ([^)]+)\);/g,
    replacement: (match, label, data) => {
      // If the data looks like it could contain sensitive info, sanitize it
      if (data.includes('data') || data.includes('task') || data.includes('user') || data.includes('profile')) {
        return `console.log('${label} completed');`;
      }
      return match;
    },
    name: 'Sanitize data-exposing console.log statements'
  },
  
  // Fix specific patterns that expose task/user data
  {
    pattern: /console\.log\('([^']*task[^']*)', [^)]+\);/gi,
    replacement: 'console.log(\'$1 completed\');',
    name: 'Sanitize task data logging'
  },
  
  {
    pattern: /console\.log\('([^']*user[^']*)', [^)]+\);/gi,
    replacement: 'console.log(\'$1 completed\');',
    name: 'Sanitize user data logging'
  },
  
  {
    pattern: /console\.log\('([^']*data[^']*)', [^)]+\);/gi,
    replacement: 'console.log(\'$1 completed\');',
    name: 'Sanitize general data logging'
  },
  
  // Fix console.log that logs objects directly
  {
    pattern: /console\.log\(([a-zA-Z_$][a-zA-Z0-9_$]*)\);/g,
    replacement: (match, varName) => {
      // If variable name suggests sensitive data, replace with safe logging
      if (/^(data|task|user|profile|settings|offer|organization)/.test(varName.toLowerCase())) {
        return `console.log('${varName} processed');`;
      }
      return match;
    },
    name: 'Replace direct object logging with safe messages'
  }
];

/**
 * Files to exclude from fixing
 */
const EXCLUDE_PATTERNS = [
  /node_modules/,
  /\.git/,
  /dist/,
  /build/,
  /fix-malformed-console-logs\.cjs$/,
  /comprehensive-security-scanner\.cjs$/,
  /automated-security-fixer\.cjs$/
];

/**
 * File extensions to fix
 */
const FIX_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

/**
 * Fix results storage
 */
let fixResults = {
  totalFiles: 0,
  fixedFiles: 0,
  totalFixes: 0,
  fixesByCategory: {},
  errors: []
};

/**
 * Log with colors
 */
function log(level, message) {
  const color = {
    ERROR: colors.red,
    WARNING: colors.yellow,
    SUCCESS: colors.green,
    INFO: colors.blue
  }[level] || colors.reset;
  
  console.log(`${color}[${level}]${colors.reset} ${message}`);
}

/**
 * Check if file should be excluded
 */
function shouldExcludeFile(filePath) {
  return EXCLUDE_PATTERNS.some(pattern => pattern.test(filePath));
}

/**
 * Check if file should be fixed
 */
function shouldFixFile(filePath) {
  const ext = path.extname(filePath);
  return FIX_EXTENSIONS.includes(ext) && !shouldExcludeFile(filePath);
}

/**
 * Apply malformed console.log fixes to file content
 */
function fixMalformedConsoleLog(content, filePath) {
  let modifiedContent = content;
  let fileFixCount = 0;
  
  // Apply each fix
  MALFORMED_FIXES.forEach(({ pattern, replacement, name }) => {
    const beforeLength = modifiedContent.length;
    
    if (typeof replacement === 'function') {
      modifiedContent = modifiedContent.replace(pattern, replacement);
    } else {
      modifiedContent = modifiedContent.replace(pattern, replacement);
    }
    
    const afterLength = modifiedContent.length;
    
    if (beforeLength !== afterLength) {
      fileFixCount++;
      fixResults.totalFixes++;
      
      if (!fixResults.fixesByCategory[name]) {
        fixResults.fixesByCategory[name] = 0;
      }
      fixResults.fixesByCategory[name]++;
      
      log('INFO', `Applied fix: ${name} in ${filePath}`);
    }
  });
  
  return { content: modifiedContent, fixCount: fileFixCount };
}

/**
 * Fix a single file
 */
function fixFile(filePath) {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    const { content: fixedContent, fixCount } = fixMalformedConsoleLog(originalContent, filePath);
    
    if (fixCount > 0) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      fixResults.fixedFiles++;
      log('SUCCESS', `Fixed ${fixCount} malformed console.log issues in ${filePath}`);
    }
    
    return true;
  } catch (error) {
    const errorMsg = `Failed to fix ${filePath}: ${error.message}`;
    fixResults.errors.push(errorMsg);
    log('ERROR', errorMsg);
    return false;
  }
}

/**
 * Recursively fix directory
 */
function fixDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath);
    
    items.forEach(item => {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !shouldExcludeFile(fullPath)) {
        fixDirectory(fullPath);
      } else if (stat.isFile() && shouldFixFile(fullPath)) {
        fixResults.totalFiles++;
        fixFile(fullPath);
      }
    });
  } catch (error) {
    const errorMsg = `Failed to fix directory ${dirPath}: ${error.message}`;
    fixResults.errors.push(errorMsg);
    log('ERROR', errorMsg);
  }
}

/**
 * Generate fix report
 */
function generateReport() {
  console.log('\n' + '='.repeat(80));
  log('INFO', colors.bold + 'MALFORMED CONSOLE.LOG FIX RESULTS' + colors.reset);
  console.log('='.repeat(80));
  
  // Summary
  console.log(`\n📊 ${colors.bold}FIX SUMMARY${colors.reset}`);
  console.log(`Total files processed: ${fixResults.totalFiles}`);
  console.log(`Files with fixes applied: ${fixResults.fixedFiles}`);
  console.log(`Total malformed console.log fixes applied: ${fixResults.totalFixes}`);
  
  // Category breakdown
  console.log(`\n🔧 ${colors.bold}FIXES BY CATEGORY${colors.reset}`);
  Object.entries(fixResults.fixesByCategory).forEach(([category, count]) => {
    console.log(`${category}: ${count} fixes`);
  });
  
  // Errors
  if (fixResults.errors.length > 0) {
    console.log(`\n❌ ${colors.bold}ERRORS${colors.reset}`);
    fixResults.errors.forEach(error => {
      console.log(`- ${error}`);
    });
  }
  
  // Success message
  if (fixResults.totalFixes > 0) {
    log('SUCCESS', `🎉 Successfully fixed ${fixResults.totalFixes} malformed console.log statements!`);
    console.log('\n💡 Next steps:');
    console.log('1. Run the security scanner again to verify remaining issues');
    console.log('2. Test your application to ensure it still works');
    console.log('3. Commit the console.log security improvements');
  } else {
    log('INFO', 'No malformed console.log statements found - your code is clean!');
  }
}

/**
 * Main execution
 */
function main() {
  console.log(`${colors.blue}🔧 Starting Malformed Console.Log Fixes...${colors.reset}\n`);
  
  const startTime = Date.now();
  
  // Fix src directory
  if (fs.existsSync('src')) {
    log('INFO', 'Fixing src/ directory...');
    fixDirectory('src');
  }
  
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);
  
  console.log(`\n⏱️ Fixes completed in ${duration} seconds`);
  
  // Generate report
  generateReport();
  
  // Save results to file
  const reportFile = 'malformed-console-fix-results.json';
  fs.writeFileSync(reportFile, JSON.stringify(fixResults, null, 2));
  log('INFO', `Detailed results saved to ${reportFile}`);
  
  // Exit with appropriate code
  const hasErrors = fixResults.errors.length > 0;
  process.exit(hasErrors ? 1 : 0);
}

// Run the fixer
main();

import { Card, CardContent } from '@/components/ui/card';
import { useOffers } from '@/hooks/use-offers';
import { useTasks } from '@/hooks/use-tasks';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { Task, Offer } from '@/services/taskService';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

const normalizeStatus = (status: string): string => {
  if (status === 'pending') return 'awaiting';
  return status;
};

interface OffersSectionProps {
  isSchool: boolean;
}

interface EnrichedOffer extends Offer {
  taskTitle: string;
  taskStatus?: string;
}

const OffersSection = ({ isSchool }: OffersSectionProps) => {
  if (process.env.NODE_ENV === 'development') {
    console.log("OffersSection rendered with isSchool:", isSchool);
  }
  const { getUserOffers, updateOfferStatus, isUpdatingOfferStatus } = useOffers();
  const { tasks, isLoadingTasks } = useTasks();
  const [offersWithTaskInfo, setOffersWithTaskInfo] = useState<EnrichedOffer[]>([]);
  const { user } = useAuth();
  const { toast } = useToast();
  const [processingOffer, setProcessingOffer] = useState<string | null>(null);

  const {
    data: userOffers,
    isLoading: isLoadingUserOffers,
    refetch: refetchOffers
  } = getUserOffers();

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
    console.log("Setting up refresh timer for offers");
  }
    const refreshTimer = setInterval(() => {
      if (process.env.NODE_ENV === 'development') {
    console.log("Auto-refreshing offers");
  }
      refetchOffers();
    }, 10000); // Increased refresh time to reduce load

    return () => {
      if (process.env.NODE_ENV === 'development') {
    console.log("Cleaning up refresh timer");
  }
      clearInterval(refreshTimer);
    };
  }, [refetchOffers]);

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
    console.log("Processing offers and tasks for display");
  }
    if (process.env.NODE_ENV === 'development') {

      console.log("userOffers:", userOffers);

      }
    if (process.env.NODE_ENV === 'development') {
    console.log("tasks:", tasks);
  }
    if (userOffers && tasks) {
      const enrichedOffers = userOffers.map(offer => {
        const relatedTask = tasks.find(task => task.id === offer.task_id);
        if (process.env.NODE_ENV === 'development') {
    console.log(`Enriching offer ${offer.id} with task info:`, relatedTask?.title);
  }
        return {
          ...offer,
          taskTitle: relatedTask?.title || `Task #${offer.task_id.substring(0, 8)}`,
          taskStatus: relatedTask?.status
        };
      });

      if (isSchool) {
        if (process.env.NODE_ENV === 'development') {
    console.log("Sorting offers for school view");
  }
        const statusOrder = { awaiting: 0, accepted: 1, rejected: 2 };
        const sortedOffers = [...enrichedOffers].sort((a, b) => {
          const statusA = normalizeStatus(a.status);
          const statusB = normalizeStatus(b.status);
          return (
            statusOrder[statusA as keyof typeof statusOrder] -
            statusOrder[statusB as keyof typeof statusOrder]
          );
        });

        if (process.env.NODE_ENV === 'development') {
    console.log("Sorted offers:", sortedOffers);
  }
        setOffersWithTaskInfo(sortedOffers);
      } else {
        if (process.env.NODE_ENV === 'development') {
    console.log("Setting unsorted offers for supplier view");
  }
        setOffersWithTaskInfo(enrichedOffers);
      }
    }
  }, [userOffers, tasks, isSchool]);

  const getStatusBadgeClasses = (status: string) => {
    const normalizedStatus = normalizeStatus(status);

    switch(normalizedStatus) {
      case 'awaiting':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'accepted':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleRejectOffer = (offerId: string) => {
    if (process.env.NODE_ENV === 'development') {
    console.log(`OffersSection.handleRejectOffer called for offer ${offerId}`);
  }
    setProcessingOffer(offerId);

    updateOfferStatus(offerId, 'rejected', {
      onSuccess: (updatedOffer) => {
        if (process.env.NODE_ENV === 'development') {
    console.log("Offer rejected successfully:", updatedOffer);
  }
        toast({
          title: "Offer rejected",
          description: "The offer has been rejected successfully."
        });
        if (process.env.NODE_ENV === 'development') {
    console.log("Manually refreshing offers after rejection");
  }
        refetchOffers();
        setProcessingOffer(null);
      },
      onError: (error) => {
        console.error('Error rejecting offer:', error);
        toast({
          variant: "destructive",
          title: "Error rejecting offer",
          description: "There was a problem rejecting this offer. Please try again."
        });
        setProcessingOffer(null);
      }
    });
  };

  if (isLoadingUserOffers || isLoadingTasks) {
    if (process.env.NODE_ENV === 'development') {
    console.log("Rendering loading state");
  }
    return (
      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-6">
            {isSchool ? 'Offers Received' : 'My Offers'}
          </h2>
          <div className="space-y-4">
            {Array(3).fill(0).map((_, i) => (
              <div key={i} className="border rounded-lg p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-1/3" />
                  <Skeleton className="h-4 w-1/5" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!offersWithTaskInfo || offersWithTaskInfo.length === 0) {
    if (process.env.NODE_ENV === 'development') {
    console.log("Rendering empty state");
  }
    return (
      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-6">
            {isSchool ? 'Task Offers Received' : 'My Submitted Offers'}
          </h2>
          <p className="text-center py-6 text-gray-500">
            {isSchool
              ? "You haven't received any offers yet."
              : "You haven't made any offers yet."}
          </p>
        </CardContent>
      </Card>
    );
  }

  if (process.env.NODE_ENV === 'development') {
    console.log("Rendering offers list with", offersWithTaskInfo.length, "offers");
  }
  return (
    <Card>
      <CardContent className="p-6">
        <h2 className="text-xl font-semibold mb-6">
          {isSchool ? 'Offers Received' : 'My Offers'}
        </h2>
        <div className="space-y-4">
          {offersWithTaskInfo.map(offer => {
            const displayStatus = normalizeStatus(offer.status);
            if (process.env.NODE_ENV === 'development') {
    console.log(`Rendering offer ${offer.id} with status ${displayStatus}`);
  }
            return (
              <div key={offer.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <Link to={`/tasks/${offer.task_id}`} className="font-medium hover:text-classtasker-blue">
                    {offer.taskTitle}
                  </Link>
                  <Badge
                    variant="outline"
                    className={getStatusBadgeClasses(offer.status)}
                  >
                    {displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1)}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600">
                    {new Date(offer.created_at).toLocaleDateString()}
                  </div>
                  <div className="font-medium text-classtasker-blue">
                    £{Number(offer.amount).toFixed(2)}
                  </div>
                </div>

                {isSchool && displayStatus === 'awaiting' && (
                  <div className="flex justify-end gap-2 mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:bg-red-50"
                      onClick={() => handleRejectOffer(offer.id)}
                      disabled={processingOffer === offer.id || isUpdatingOfferStatus}
                    >
                      {processingOffer === offer.id ? (
                        <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Rejecting...</>
                      ) : (
                        'Reject'
                      )}
                    </Button>
                    <Button
                      variant="default"
                      size="sm"
                      className="bg-classtasker-blue hover:bg-blue-600"
                    >
                      <Link to={`/tasks/${offer.task_id}`}>View Details</Link>
                    </Button>
                  </div>
                )}

                {offer.taskStatus && offer.taskStatus !== 'open' && (
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    Task Status: {offer.taskStatus.charAt(0).toUpperCase() + offer.taskStatus.slice(1)}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default OffersSection;

/**
 * PWA Utility Functions
 *
 * This file contains utility functions for Progressive Web App functionality,
 * including offline detection, PWA detection, and data caching.
 */

// PWA detection - checks if the app is running in standalone mode
export const isPWA = (): boolean => {
  try {
    if (typeof window === 'undefined') return false;

    // Check for standalone display mode (most browsers)
    if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
      return true;
    }

    // Check for iOS standalone mode
    if (window.navigator && (window.navigator as any).standalone === true) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('[PWA] Error checking PWA mode:', error);
    return false;
  }
};

// Online status detection
export const isOnline = (): boolean => {
  try {
    if (typeof navigator === 'undefined') return true;
    return navigator.onLine;
  } catch (error) {
    console.error('[PWA] Error checking online status:', error);
    return true; // Default to online if there's an error
  }
};

// Register connectivity listeners
export const registerConnectivityListeners = (
  onlineCallback: () => void,
  offlineCallback: () => void
): () => void => {
  const handleOnline = () => {
    if (process.env.NODE_ENV === 'development') {
      console.log('[PWA] Device is online');
      }
    onlineCallback();
  };

  const handleOffline = () => {
    if (process.env.NODE_ENV === 'development') {
      console.log('[PWA] Device is offline');
      }
    offlineCallback();
  };

  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);

  // Return cleanup function
  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
};

// LocalStorage keys
const CHAT_MESSAGES_KEY_PREFIX = 'pwa_chat_messages_';
const CHAT_THREADS_KEY = 'pwa_chat_threads';
const TASKS_KEY = 'pwa_tasks';
const USER_TASKS_KEY_PREFIX = 'pwa_user_tasks_';
const DASHBOARD_DATA_KEY = 'pwa_dashboard_data';
const PROFILE_KEY_PREFIX = 'pwa_profile_';

// Cache chat messages for a specific thread
export const storeChatMessages = (threadId: string, messages: any[]): void => {
  try {
    localStorage.setItem(
      `${CHAT_MESSAGES_KEY_PREFIX}${threadId}`,
      JSON.stringify({
        timestamp: Date.now(),
        messages
      })
    );
    if (process.env.NODE_ENV === 'development') {
      console.log(`[PWA] Cached ${messages.length} messages for thread ${threadId}`);
      }
  } catch (error) {
    console.error('[PWA] Error caching chat messages:', error);
  }
};

// Get cached chat messages for a specific thread
export const getCachedChatMessages = (threadId: string): any[] => {
  try {
    const cachedData = localStorage.getItem(`${CHAT_MESSAGES_KEY_PREFIX}${threadId}`);
    if (!cachedData) return [];

    const { messages, timestamp } = JSON.parse(cachedData);

    // Check if cache is older than 24 hours
    if (Date.now() - timestamp > 24 * 60 * 60 * 1000) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWA] Chat message cache is older than 24 hours, clearing');
        }
      localStorage.removeItem(`${CHAT_MESSAGES_KEY_PREFIX}${threadId}`);
      return [];
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`[PWA] Retrieved ${messages.length} cached messages for thread ${threadId}`);
  }
    return messages;
  } catch (error) {
    console.error('[PWA] Error retrieving cached chat messages:', error);
    return [];
  }
};

// Cache chat threads
export const storeChatThreads = (threads: any[]): void => {
  try {
    localStorage.setItem(
      CHAT_THREADS_KEY,
      JSON.stringify({
        timestamp: Date.now(),
        threads
      })
    );
    if (process.env.NODE_ENV === 'development') {
      console.log(`[PWA] Cached ${threads.length} chat threads`);
      }
  } catch (error) {
    console.error('[PWA] Error caching chat threads:', error);
  }
};

// Get cached chat threads
export const getCachedChatThreads = (): any[] => {
  try {
    const cachedData = localStorage.getItem(CHAT_THREADS_KEY);
    if (!cachedData) return [];

    const { threads, timestamp } = JSON.parse(cachedData);

    // Check if cache is older than 24 hours
    if (Date.now() - timestamp > 24 * 60 * 60 * 1000) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWA] Chat threads cache is older than 24 hours, clearing');
        }
      localStorage.removeItem(CHAT_THREADS_KEY);
      return [];
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`[PWA] Retrieved ${threads.length} cached chat threads`);
  }
    return threads;
  } catch (error) {
    console.error('[PWA] Error retrieving cached chat threads:', error);
    return [];
  }
};

// Cache tasks
export const storeTasks = (tasks: any[]): void => {
  try {
    localStorage.setItem(
      TASKS_KEY,
      JSON.stringify({
        timestamp: Date.now(),
        tasks
      })
    );
    if (process.env.NODE_ENV === 'development') {
      console.log(`[PWA] Cached ${tasks.length} tasks`);
      }
  } catch (error) {
    console.error('[PWA] Error caching tasks:', error);
  }
};

// Get cached tasks
export const getCachedTasks = (): any[] => {
  try {
    const cachedData = localStorage.getItem(TASKS_KEY);
    if (!cachedData) return [];

    const { tasks, timestamp } = JSON.parse(cachedData);

    // Check if cache is older than 24 hours
    if (Date.now() - timestamp > 24 * 60 * 60 * 1000) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWA] Tasks cache is older than 24 hours, clearing');
        }
      localStorage.removeItem(TASKS_KEY);
      return [];
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`[PWA] Retrieved ${tasks.length} cached tasks`);
  }
    return tasks;
  } catch (error) {
    console.error('[PWA] Error retrieving cached tasks:', error);
    return [];
  }
};

// Cache user-specific tasks
export const storeUserTasks = (userId: string, tasks: any[]): void => {
  try {
    localStorage.setItem(
      `${USER_TASKS_KEY_PREFIX}${userId}`,
      JSON.stringify({
        timestamp: Date.now(),
        tasks
      })
    );
    if (process.env.NODE_ENV === 'development') {
      console.log(`[PWA] Cached ${tasks.length} tasks for user ${userId}`);
      }
  } catch (error) {
    console.error('[PWA] Error caching user tasks:', error);
  }
};

// Get cached user-specific tasks
export const getCachedUserTasks = (userId: string): any[] => {
  try {
    const cachedData = localStorage.getItem(`${USER_TASKS_KEY_PREFIX}${userId}`);
    if (!cachedData) return [];

    const { tasks, timestamp } = JSON.parse(cachedData);

    // Check if cache is older than 24 hours
    if (Date.now() - timestamp > 24 * 60 * 60 * 1000) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWA] User tasks cache is older than 24 hours, clearing');
        }
      localStorage.removeItem(`${USER_TASKS_KEY_PREFIX}${userId}`);
      return [];
    }

    if (process.env.NODE_ENV === 'development') {

      console.log(`[PWA] Retrieved ${tasks.length} cached tasks for user ${userId}`);

      }
    return tasks;
  } catch (error) {
    console.error('[PWA] Error retrieving cached user tasks:', error);
    return [];
  }
};

// Cache dashboard data
export const storeDashboardData = (data: any): void => {
  try {
    localStorage.setItem(
      DASHBOARD_DATA_KEY,
      JSON.stringify({
        timestamp: Date.now(),
        data
      })
    );
    if (process.env.NODE_ENV === 'development') {
      console.log('[PWA] Cached dashboard data');
      }
  } catch (error) {
    console.error('[PWA] Error caching dashboard data:', error);
  }
};

// Get cached dashboard data
export const getCachedDashboardData = (): any => {
  try {
    const cachedData = localStorage.getItem(DASHBOARD_DATA_KEY);
    if (!cachedData) return null;

    const { data, timestamp } = JSON.parse(cachedData);

    // Check if cache is older than 24 hours
    if (Date.now() - timestamp > 24 * 60 * 60 * 1000) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWA] Dashboard data cache is older than 24 hours, clearing');
        }
      localStorage.removeItem(DASHBOARD_DATA_KEY);
      return null;
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('[PWA] Retrieved cached dashboard data');
  }
    return data;
  } catch (error) {
    console.error('[PWA] Error retrieving cached dashboard data:', error);
    return null;
  }
};

// Cache user profile data
export const storeProfileData = (userId: string, profileData: any): void => {
  try {
    localStorage.setItem(
      `${PROFILE_KEY_PREFIX}${userId}`,
      JSON.stringify({
        timestamp: Date.now(),
        profile: profileData
      })
    );
    if (process.env.NODE_ENV === 'development') {
      console.log(`[PWA] Cached profile data for user ${userId}`);
      }
  } catch (error) {
    console.error('[PWA] Error caching profile data:', error);
  }
};

// Get cached profile data
export const getCachedProfileData = (userId: string): any => {
  try {
    const cachedData = localStorage.getItem(`${PROFILE_KEY_PREFIX}${userId}`);
    if (!cachedData) return null;

    const { profile, timestamp } = JSON.parse(cachedData);

    // Check if cache is older than 1 hour
    if (Date.now() - timestamp > 60 * 60 * 1000) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWA] Profile data cache is older than 1 hour, clearing');
        }
      localStorage.removeItem(`${PROFILE_KEY_PREFIX}${userId}`);
      return null;
    }

    if (process.env.NODE_ENV === 'development') {

      console.log('[PWA] Retrieved cached profile data for user completed');

      }
    return profile;
  } catch (error) {
    console.error('[PWA] Error retrieving cached profile data:', error);
    return null;
  }
};

// Clear profile cache for a user
export const clearProfileCache = (userId: string): void => {
  try {
    localStorage.removeItem(`${PROFILE_KEY_PREFIX}${userId}`);
    if (process.env.NODE_ENV === 'development') {
      console.log(`[PWA] Cleared profile cache for user ${userId}`);
      }
  } catch (error) {
    console.error('[PWA] Error clearing profile cache:', error);
  }
};

// Clear all PWA caches
export const clearAllPWACaches = (): void => {
  try {
    // Get all localStorage keys
    const keys = Object.keys(localStorage);

    // Filter PWA-related keys
    const pwaKeys = keys.filter(key =>
      key.startsWith(CHAT_MESSAGES_KEY_PREFIX) ||
      key === CHAT_THREADS_KEY ||
      key === TASKS_KEY ||
      key.startsWith(USER_TASKS_KEY_PREFIX) ||
      key === DASHBOARD_DATA_KEY
    );

    // Remove all PWA-related keys
    pwaKeys.forEach(key => localStorage.removeItem(key));

    if (process.env.NODE_ENV === 'development') {
    console.log(`[PWA] Cleared ${pwaKeys.length} PWA cache entries`);
  }
  } catch (error) {
    console.error('[PWA] Error clearing PWA caches:', error);
  }
};

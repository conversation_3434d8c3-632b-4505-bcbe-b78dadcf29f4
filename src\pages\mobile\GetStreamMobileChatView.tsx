/**
 * GetStream Mobile Chat View Component
 *
 * A mobile-optimized implementation of the chat view using GetStream
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Send, Info, WifiOff } from 'lucide-react';
import { useGetStreamChat } from '@/hooks/use-getstream-chat';
import { Channel, MessageResponse } from 'stream-chat';
import {
  Chat,
  MessageInput,
  MessageList,
  Window,
} from 'stream-chat-react';

// Import GetStream CSS - using only the official GetStream styles
import 'stream-chat-react/dist/css/v2/index.css';
import '@stream-io/stream-chat-css/dist/v2/css/index.css';

// Import custom CSS for GetStream chat
import '@/styles/getstream-chat.css';

// Custom styles for mobile
import './GetStreamMobileChatView.css';

const GetStreamMobileChatView: React.FC = () => {
  // Get URL parameters
  const { threadId } = useParams<{ threadId: string }>();
  const [searchParams] = useSearchParams();
  const taskId = searchParams.get('task');
  const navigate = useNavigate();
  const { user } = useAuth();

  // Local state
  const [offlineMode, setOfflineMode] = useState(!navigator.onLine);
  const [taskTitle, setTaskTitle] = useState<string>('Chat');
  const [taskStatus, setTaskStatus] = useState<string>('');

  // Use the GetStream chat hook
  const {
    client,
    channel,
    messages,
    isLoading,
    isSending,
    sendMessage
  } = useGetStreamChat({
    taskId: taskId || '',
    threadId
  });

  // Log parameters for debugging
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {

      console.log('GetStreamMobileChatView mounted with: completed');


      }
    // Check if we're offline
    if (!navigator.onLine) {
      setOfflineMode(true);
    }

    // Register listeners for online/offline events
    const handleOnline = () => {
      if (process.env.NODE_ENV === 'development') {
    console.log('GetStreamMobileChatView: Back online');
  }
      setOfflineMode(false);
    };

    const handleOffline = () => {
      if (process.env.NODE_ENV === 'development') {
    console.log('GetStreamMobileChatView: Went offline');
  }
      setOfflineMode(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Fetch task details if available
    if (channel) {
      const channelData = channel.data || {};
      if (channelData.name) {
        setTaskTitle(channelData.name);
      }

      // You could also fetch task status from Supabase here
    }

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);

      if (process.env.NODE_ENV === 'development') {
    console.log('GetStreamMobileChatView unmounting, cleaning up resources for: completed');
  }
    };
  }, [threadId, taskId, user, channel]);

  // Custom message input handler
  const handleSendMessage = async (text: string) => {
    if (!text.trim() || !taskId || !user) {
      if (process.env.NODE_ENV === 'development') {
    console.log('Cannot send message: missing data');
  }
      return;
    }

    try {
      const result = await sendMessage(text.trim());

      if (result.success) {
        if (process.env.NODE_ENV === 'development') {
    console.log('Message sent successfully');
  }
      } else {
        if (process.env.NODE_ENV === 'development') {
    console.log(`Failed to send message: ${result.reason}`);
  }
        if (result.reason === 'thread_closed') {
          alert('This conversation is closed. You cannot send new messages.');
        } else {
          alert('Failed to send message. Please try again.');
        }
      }
    } catch (err) {
      console.error('Error sending message:', err);
      alert('Failed to send message. Please try again.');
    }
  };

  // Custom message input component
  const CustomInput = () => {
    const [text, setText] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (text.trim()) {
        handleSendMessage(text);
        setText('');
      }
    };

    return (
      <form onSubmit={handleSubmit} className="mobile-message-input">
        <input
          type="text"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder={offlineMode ? "Can't send messages while offline" : "Type a message..."}
          disabled={isSending || offlineMode}
        />
        <Button
          type="submit"
          size="icon"
          disabled={isSending || !text.trim() || offlineMode}
          className={`send-button ${!text.trim() || offlineMode ? 'disabled' : ''}`}
        >
          <Send size={18} />
        </Button>
      </form>
    );
  };

  return (
    <div className="mobile-chat-container">
      {/* Header */}
      <div className="mobile-chat-header">
        <div className="header-left">
          <button
            onClick={() => navigate('/mobile/chats')}
            className="back-button"
          >
            <ArrowLeft size={20} />
          </button>

          <div className="header-info">
            <Avatar className="header-avatar">
              <AvatarFallback className="avatar-fallback">
                {taskTitle.substring(0, 2).toUpperCase() || 'T'}
              </AvatarFallback>
            </Avatar>

            <div>
              <h1 className="header-title">
                {isLoading ? <Skeleton className="h-4 w-32" /> : taskTitle}
              </h1>
              {taskStatus && (
                <p className="header-status">
                  {taskStatus.replace('_', ' ').charAt(0).toUpperCase() + taskStatus.replace('_', ' ').slice(1)}
                </p>
              )}
            </div>
          </div>
        </div>

        <button
          onClick={() => navigate(`/tasks/${taskId}`)}
          className="info-button"
          aria-label="Task details"
        >
          <Info size={20} />
        </button>
      </div>

      {/* Offline Banner */}
      {offlineMode && (
        <div className="offline-banner">
          <WifiOff size={16} className="offline-icon" />
          <span className="offline-text">Offline Mode - Messages will be sent when you're back online</span>
        </div>
      )}

      {/* Chat Content */}
      <div className="mobile-chat-content">
        {isLoading || !client || !channel ? (
          // Loading skeletons
          <div className="loading-skeletons">
            {[...Array(5)].map((_, i) => (
              <div key={i} className={`skeleton-message ${i % 2 === 0 ? 'left' : 'right'}`}>
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-20 mt-2" />
              </div>
            ))}
          </div>
        ) : (
          // GetStream Chat Component
          <Chat client={client} theme="messaging light">
            <Channel channel={channel}>
              <Window>
                <MessageList />
                <CustomInput />
              </Window>
            </Channel>
          </Chat>
        )}
      </div>
    </div>
  );
};

export default GetStreamMobileChatView;

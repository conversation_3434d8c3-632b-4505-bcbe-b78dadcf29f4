import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Loader2, AlertCircle, CheckCircle } from 'lucide-react';

/**
 * AuthCallback component handles all authentication redirects from Supabase
 * This includes password reset, email verification, and OAuth callbacks
 */
const AuthCallback: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [authType, setAuthType] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        if (process.env.NODE_ENV === 'development') {
          console.log('AuthCallback: Processing auth redirect');
          }
        if (process.env.NODE_ENV === 'development') {
          console.log('URL:', window.location.href);
        
          }
        // Parse URL parameters
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const queryParams = new URLSearchParams(window.location.search);
        
        // Check what type of auth flow this is
        const hashType = hashParams.get('type');
        const queryType = queryParams.get('type');
        const type = hashType || queryType;
        
        if (process.env.NODE_ENV === 'development') {
    console.log('Auth type:', type);
  }
        setAuthType(type);
        
        // Handle password recovery flow
        if (type === 'recovery' || type === 'passwordRecovery') {
          if (process.env.NODE_ENV === 'development') {
            console.log('Processing password recovery callback');
          
            }
          // Check for tokens in hash
          const accessToken = hashParams.get('access_token');
          const refreshToken = hashParams.get('refresh_token');
          
          if (accessToken && refreshToken) {
            if (process.env.NODE_ENV === 'development') {
              console.log('Found tokens in URL hash');
            
              }
            // Set the session from the recovery tokens
            const { error } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken
            });
            
            if (error) {
              console.error('Error setting session from hash tokens:', error);
              throw error;
            }
            
            // Redirect to password reset page
            navigate('/reset-password/confirm', { replace: true });
            return;
          }
          
          // Check for token in query parameters
          const token = queryParams.get('token');
          
          if (token) {
            if (process.env.NODE_ENV === 'development') {
              console.log('Found token in query parameters');
            
              }
            try {
              // Try to exchange the token for a session
              const { error } = await supabase.auth.verifyOtp({
                token_hash: token,
                type: 'recovery'
              });
              
              if (error) {
                console.error('Error verifying OTP:', error);
                throw error;
              }
              
              // Redirect to password reset page
              navigate('/reset-password/confirm', { replace: true });
              return;
            } catch (verifyError) {
              console.error('Error verifying recovery token:', verifyError);
              setError('Invalid or expired password reset link. Please request a new one.');
            }
          }
        }
        
        // If we reach here and haven't redirected, check if we have a session
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session) {
          if (process.env.NODE_ENV === 'development') {
            console.log('Active session found, redirecting to appropriate page');
          
            }
          if (type === 'recovery') {
            navigate('/reset-password/confirm', { replace: true });
          } else {
            // For other auth types, redirect to dashboard
            navigate('/dashboard', { replace: true });
          }
          return;
        }
        
        // If we reach here, we couldn't process the auth callback
        if (process.env.NODE_ENV === 'development') {
          console.log('No valid auth parameters found');
          }
        setError('Invalid or expired authentication link. Please try again.');
      } catch (err: any) {
        console.error('Error processing auth callback:', err);
        setError(err.message || 'An error occurred while processing your request.');
      } finally {
        setLoading(false);
      }
    };
    
    handleAuthCallback();
  }, [navigate, location]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-bold tracking-tight text-gray-900">
            Processing Authentication
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Please wait while we process your request
          </p>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Authentication {authType ? `(${authType})` : ''}</CardTitle>
            <CardDescription>
              {loading ? 'Processing your authentication request...' : 
                error ? 'There was a problem with your request' : 
                'Authentication successful'}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="flex flex-col items-center justify-center p-6">
            {loading ? (
              <div className="flex flex-col items-center space-y-4">
                <Loader2 className="h-12 w-12 animate-spin text-primary" />
                <p className="text-sm text-gray-500">This may take a few moments...</p>
              </div>
            ) : error ? (
              <div className="space-y-4 w-full">
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
                <div className="flex justify-center mt-4">
                  <Button onClick={() => navigate('/forgot-password')}>
                    Try Again
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center space-y-4">
                <CheckCircle className="h-12 w-12 text-green-500" />
                <p className="text-sm text-gray-500">Authentication successful! Redirecting...</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AuthCallback;

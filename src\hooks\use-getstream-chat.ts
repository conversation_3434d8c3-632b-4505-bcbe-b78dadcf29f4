/**
 * Hook for using GetStream chat functionality
 */

import { useState, useEffect, useCallback } from 'react';
import { Channel, StreamChat, Message as StreamMessage } from 'stream-chat';
import { useAuth } from '@/contexts/AuthContext';
import {
  getStreamClient,
  connectUser,
  disconnectUser,
  getTaskChannel,
  generateToken,
  incrementConnectionCount,
  decrementConnectionCount
} from '@/integrations/getstream/client';
import { supabase } from '@/integrations/supabase/client';
import { isPWA } from '@/utils/pwa-utils';

export interface UseGetStreamChatProps {
  taskId: string;
  threadId?: string;
}

export interface UseGetStreamChatResult {
  client: StreamChat | null;
  channel: Channel | null;
  messages: StreamMessage[];
  isLoading: boolean;
  isSending: boolean;
  error: Error | null;
  sendMessage: (text: string) => Promise<{ success: boolean; reason?: string }>;
}

/**
 * Hook for using GetStream chat functionality
 */
export function useGetStreamChat({ taskId, threadId }: UseGetStreamChatProps): UseGetStreamChatResult {
  const [client, setClient] = useState<StreamChat | null>(null);
  const [channel, setChannel] = useState<Channel | null>(null);
  const [messages, setMessages] = useState<StreamMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { user, profile } = useAuth();

  // Initialize the client and channel
  useEffect(() => {
    // Log environment information (development only)
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log('[useGetStreamChat] Environment info:', {
        isPWA: isPWA(),
        isOnline: typeof navigator !== 'undefined' ? navigator.onLine : 'unknown',
        userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
        apiKey: import.meta.env.VITE_GETSTREAM_API_KEY ? 'present' : 'missing',
        origin: typeof window !== 'undefined' ? window.location.origin : 'not in browser',
        hostname: typeof window !== 'undefined' ? window.location.hostname : 'not in browser',
        protocol: typeof window !== 'undefined' ? window.location.protocol : 'not in browser',
        pathname: typeof window !== 'undefined' ? window.location.pathname : 'not in browser'
      });

        }
    }

    if (!user) {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {

          console.log('[useGetStreamChat] Missing user completed');

          }
      }
      setIsLoading(false);
      return;
    }

    // If we have neither taskId nor threadId, we can't proceed
    if (!taskId && !threadId) {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Missing both taskId and threadId');
  }
      }
      setIsLoading(false);
      return;
    }

    // Check if we're in PWA mode
    const inPWAMode = isPWA();

    // Remove idle timeout - let the chat connect naturally and reconnect automatically when needed

    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {

        console.log('[useGetStreamChat] Initializing chat with params: completed');

        }
    }

    let mounted = true;
    let activeChannel: Channel | null = null;

    const initializeChat = async () => {
      try {
        if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Initializing chat with params: completed');
  }
        setIsLoading(true);

        // Increment connection count
        incrementConnectionCount();

        // Get user display name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        // Connect the user to Stream using server-generated token
        let streamClient;
        try {
          if (process.env.NODE_ENV === 'development') {
            if (process.env.NODE_ENV === 'development') {

              console.log('[useGetStreamChat] Connecting user to Stream: completed');

              }
          }
          streamClient = await connectUser(
            user.id,
            userName,
            user.id, // This is just a placeholder, the actual token is generated server-side
            profile?.avatar_url || undefined
          );
        } catch (tokenError) {
          console.error('[useGetStreamChat] Error connecting user to Stream:', tokenError);

          // Add a retry for token generation
          if (process.env.NODE_ENV === 'development') {
            if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Retrying token generation after short delay');
  }
          }
          await new Promise(resolve => setTimeout(resolve, 1000));

          streamClient = await connectUser(
            user.id,
            userName,
            user.id,
            profile?.avatar_url || undefined
          );
        }

        if (!mounted) return;
        setClient(streamClient);

        // If we have a threadId but no taskId, connect directly to the thread
        if (threadId && !taskId) {
          if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Using threadId without taskId: completed');
  }
          try {
            // Connect to the channel using the threadId directly
            const threadChannel = streamClient.channel('messaging', threadId);
            await threadChannel.watch();

            if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Successfully connected to thread:', threadId);
  }
            if (!mounted) return;
            setChannel(threadChannel);
            activeChannel = threadChannel;

            // Load messages - use a smaller limit for PWA
            const inPWAMode = isPWA();
            if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Loading messages for thread channel, PWA mode:', inPWAMode);
  }
            const response = await threadChannel.query({
              messages: { limit: inPWAMode ? 15 : 30 }
            });

            if (!mounted) return;
            setMessages(response.messages || []);
            setIsLoading(false);

            // Set up message listener
            const messageListener = threadChannel.on('message.new', (event) => {
              setMessages((prevMessages) => [...prevMessages, event.message]);
            });

            return () => {
              messageListener.unsubscribe();
            };
          } catch (err) {
            console.error('[useGetStreamChat] Error connecting to thread:', err);
            if (mounted) {
              setError(err instanceof Error ? err : new Error(`Could not connect to chat: ${String(err)}`));
              setIsLoading(false);
            }
            return;
          }
        }

        // Determine the channel ID
        let channelId: string;

        // If we have a threadId, use it directly (for direct channel access)
        if (threadId) {
          channelId = threadId;
          if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Using provided threadId as channelId:', channelId);
  }
        }
        // If we have a taskId, check if the task has been migrated to GetStream
        else if (taskId) {
          try {
            const { data: task, error: taskError } = await supabase
              .from('tasks')
              .select('chat_migrated_to_stream, getstream_channel_id')
              .eq('id', taskId)
              .single();

            if (taskError) {
              console.error('[useGetStreamChat] Error fetching task:', taskError);
              // Don't throw here, just use the default channel ID format
              channelId = `task-${taskId}`;
              if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Error fetching task, using default channel ID format: completed');
  }
            } else if (task && task.chat_migrated_to_stream && task.getstream_channel_id) {
              // Use the existing channel ID
              channelId = task.getstream_channel_id;
              if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Using existing channel ID:', channelId);
  }
            } else {
              // Use the default channel ID format
              channelId = `task-${taskId}`;
              if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Using default channel ID format:', channelId);
  }
              // Try to update the task to mark it as migrated
              try {
                await supabase
                  .from('tasks')
                  .update({
                    chat_migrated_to_stream: true,
                    getstream_channel_id: channelId,
                    updated_at: new Date().toISOString(),
                  })
                  .eq('id', taskId);
              } catch (updateError) {
                console.error('[useGetStreamChat] Error updating task:', updateError);
                // Continue anyway, this is not critical
              }
            }
          } catch (error) {
            console.error('[useGetStreamChat] Error in task check:', error);
            // Use the default channel ID format as fallback
            channelId = `task-${taskId}`;
          }
        } else {
          // This shouldn't happen due to our earlier checks, but just in case
          throw new Error('No channelId could be determined');
        }

        // Get the channel
        if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Creating channel object with ID:', channelId);
  }
        const taskChannel = streamClient.channel('messaging', channelId);

        // Log channel object details
        if (process.env.NODE_ENV === 'development') {

          console.log('[useGetStreamChat] Channel object created:', {
          id: taskChannel.id,
          type: taskChannel.type,
          cid: taskChannel.cid,
          initialized: taskChannel.initialized,
          client: !!taskChannel.client,
          clientUserID: taskChannel.client?.userID,
          clientConnected: taskChannel.client?.isConnected?.()
        });


          }
        try {
          // Try to watch the channel
          if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Attempting to watch channel:', channelId);
  }
          const watchResponse = await taskChannel.watch();
          if (process.env.NODE_ENV === 'development') {

            console.log('[useGetStreamChat] Successfully watched channel:', channelId, {
            responseStatus: watchResponse?.status,
            members: Object.keys(watchResponse?.members || {}),
            messageCount: watchResponse?.messages?.length || 0
          });

            }
        } catch (channelError) {
          console.error('[useGetStreamChat] Error watching channel:', {
            channelId,
            error: channelError,
            message: channelError instanceof Error ? channelError.message : String(channelError),
            stack: channelError instanceof Error ? channelError.stack : undefined
          });

          // If the channel doesn't exist, create it
          if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Channel not found, attempting to create it');
  }
          // Default channel data
          let channelName = 'Chat';
          const members = [user.id];
          let taskData = {};

          // If we have a taskId, try to get task details
          if (taskId) {
            try {
              if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Creating new channel for task: completed');
  }
              // Get task details for the channel name
              const { data: taskDetails, error: taskDetailsError } = await supabase
                .from('tasks')
                .select('title, user_id, assigned_to')
                .eq('id', taskId)
                .single();

              if (taskDetailsError) {
                console.error('[useGetStreamChat] Error fetching task details:', taskDetailsError);
              } else if (taskDetails) {
                // Set channel name from task title
                channelName = taskDetails.title || `Task ${taskId}`;

                // Add task members
                if (taskDetails.user_id && !members.includes(taskDetails.user_id)) {
                  members.push(taskDetails.user_id);
                }
                if (taskDetails.assigned_to && !members.includes(taskDetails.assigned_to)) {
                  members.push(taskDetails.assigned_to);
                }

                // Add task data
                taskData = { task_id: taskId };
              }
            } catch (error) {
              console.error('[useGetStreamChat] Error getting task details for channel creation:', error);
            }
          } else if (threadId) {
            // For direct channel access without a task
            channelName = `Chat ${threadId}`;
            if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Creating new channel with ID:', threadId);
  }
          }

          // Convert members array to object format required by GetStream
          const membersObject = {};
          members.forEach(member => {
            membersObject[member] = { role: 'member' };
          });

          // Create the channel with available data and explicit permissions
          await taskChannel.create({
            name: channelName,
            members: membersObject,
            ...taskData,
            // Add explicit permissions for the channel - all members can read and write
            permissions: [
              { name: 'read', resources: ['channel', 'message'], roles: ['channel_member', 'channel_moderator', 'admin'] },
              { name: 'write', resources: ['channel', 'message'], roles: ['channel_member', 'channel_moderator', 'admin'] }
            ],
            // Make sure ALL members are moderators so they can initiate chat
            role_assignments: Object.keys(membersObject).reduce((acc, memberId) => {
              acc[memberId] = 'channel_moderator';
              return acc;
            }, {} as Record<string, string>)
          });

          // Add a system message
          await taskChannel.sendMessage({
            text: 'Chat initialized with GetStream.',
            user_id: user.id,
            type: 'system',
          });

          // Watch the channel again
          await taskChannel.watch();
        }

        if (!mounted) return;
        setChannel(taskChannel);
        activeChannel = taskChannel;

        // Load messages - use a smaller limit for PWA
        const inPWAMode = isPWA();
        if (process.env.NODE_ENV === 'development') {

          console.log('[useGetStreamChat] Querying channel for messages:', {
          channelId: taskChannel.id,
          isPWA: inPWAMode,
          limit: inPWAMode ? 20 : 50,
          isConnected: taskChannel.client?.isConnected?.()
        });


          }
        try {
          const response = await taskChannel.query({
            messages: { limit: inPWAMode ? 20 : 50 }
          });

          if (process.env.NODE_ENV === 'development') {


            console.log('[useGetStreamChat] Channel query response:', {
            channelId: taskChannel.id,
            messageCount: response.messages?.length || 0,
            hasMessages: !!response.messages && response.messages.length > 0,
            firstMessageId: response.messages && response.messages.length > 0 ? response.messages[0].id : null,
            members: Object.keys(response.members || {})
          });



            }
          if (!mounted) return;
          setMessages(response.messages || []);
          setIsLoading(false);
        } catch (queryError) {
          console.error('[useGetStreamChat] Error querying channel for messages:', {
            channelId: taskChannel.id,
            error: queryError,
            message: queryError instanceof Error ? queryError.message : String(queryError)
          });

          if (!mounted) return;
          setMessages([]);
          setError(queryError instanceof Error ? queryError : new Error(`Failed to load messages: ${String(queryError)}`));
          setIsLoading(false);
        }

        // Set up message listener
        const messageListener = taskChannel.on('message.new', (event) => {
          setMessages((prevMessages) => [...prevMessages, event.message]);
        });

        return () => {
          messageListener.unsubscribe();
        };
      } catch (err) {
        console.error('[useGetStreamChat] Error initializing chat:', err);
        if (mounted) {
          setError(err instanceof Error ? err : new Error(String(err)));
          setIsLoading(false);

          // Decrement connection count on error
          decrementConnectionCount(false);
        }
      }
    };

    initializeChat();

    return () => {
      mounted = false;
      if (activeChannel) {
        activeChannel.stopWatching();
      }

      // Decrement connection count
      decrementConnectionCount(true);
    };
  }, [taskId, user, profile]);

  // Add a reconnection mechanism for PWA
  useEffect(() => {
    if (!client || !user) return;

    // Check if client exists but is disconnected
    const checkAndReconnect = async () => {
      try {
        // Safely check if client is connected
        let isConnected = false;
        try {
          // First check if isConnected is a function
          if (typeof client.isConnected === 'function') {
            isConnected = client.isConnected();
          } else {
            // If not a function, check if the client has a wsConnection
            isConnected = !!client.wsConnection;
            if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Using wsConnection to determine connection status:', isConnected);
  }
          }
        } catch (checkError) {
          console.error('[useGetStreamChat] Error checking connection status:', checkError);
          // Assume disconnected if we can't check
          isConnected = false;
        }

        if (process.env.NODE_ENV === 'development') {


          console.log('[useGetStreamChat] Checking connection status: completed');



          }
        // Check if we're in PWA mode
        const inPWAMode = isPWA();
        if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Connection check - PWA mode:', inPWAMode);
  }
        if (!isConnected) {
          if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Client disconnected, attempting to reconnect');
  }
          // Get user display name
          const userName = profile?.first_name && profile?.last_name
            ? `${profile.first_name} ${profile.last_name}`
            : user.email?.split('@')[0] || 'User';

          // In PWA mode, never disconnect first
          if (!inPWAMode) {
            try {
              // Only disconnect in non-PWA mode
              await client.disconnectUser();
              if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Successfully disconnected before reconnection');
  }
            } catch (disconnectError) {
              console.error('[useGetStreamChat] Error disconnecting before reconnection:', disconnectError);
              // Continue anyway, as we want to try reconnecting
            }
          }

          // Increment connection count for reconnection
          incrementConnectionCount();

          // Use the connectUser function which handles token generation
          try {
            await connectUser(
              user.id,
              userName,
              user.id, // This is just a placeholder, the actual token is generated server-side
              profile?.avatar_url || undefined
            );

            if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Successfully reconnected client');
  }
          } catch (connectError) {
            console.error('[useGetStreamChat] Error reconnecting client:', connectError);
            // Try one more time with a delay
            await new Promise(resolve => setTimeout(resolve, 2000));

            try {
              // Generate a new token directly
              const token = await generateToken(user.id);

              // Reconnect the user
              await client.connectUser(
                {
                  id: user.id,
                  name: userName,
                  image: profile?.avatar_url || undefined
                },
                token
              );
            } catch (secondError) {
              console.error('[useGetStreamChat] Error in second reconnection attempt:', secondError);
              // Decrement connection count on error
              decrementConnectionCount(false);
              throw secondError;
            }
          }

          if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Successfully reconnected client');
  }
          // If we have a channel, reinitialize it
          if (channel) {
            if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Reinitializing channel after reconnection');
  }
            // Get a fresh channel instance
            const channelId = channel.id;
            const reconnectedChannel = client.channel('messaging', channelId);

            try {
              // Watch the channel again
              await reconnectedChannel.watch();
              if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Successfully rewatched channel after reconnection');
  }
              // Update the channel state
              setChannel(reconnectedChannel);

              // Reload messages
              const response = await reconnectedChannel.query({
                messages: { limit: 30 }
              });

              setMessages(response.messages || []);
              if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Successfully reloaded messages after reconnection');
  }
            } catch (channelError) {
              console.error('[useGetStreamChat] Error watching channel after reconnection:', channelError);
              setError(new Error(`Failed to reconnect to channel: ${channelError.message}`));
            }
          }
        }
      } catch (error) {
        console.error('[useGetStreamChat] Reconnection failed:', error);
        setError(error instanceof Error ? error : new Error(String(error)));
      }
    };

    // Check connection status when this effect runs
    checkAndReconnect();

    // Also check when app comes back to foreground
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] App returned to foreground, checking connection');
  }
        checkAndReconnect();
      }
    };

    // Also check when device comes back online
    const handleOnline = () => {
      if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Device came online, checking connection');
  }
      checkAndReconnect();
    };

    // Register event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('online', handleOnline);

    // Clean up event listeners
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('online', handleOnline);
    };
  }, [client, user, channel, profile]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Component unmounting');
  }
      // We'll use the disconnectUser function which has all the PWA/mobile checks built in
      // This function will NOT disconnect in PWA mode or on mobile browsers
      try {
        disconnectUser(false);
      } catch (error) {
        console.error('[useGetStreamChat] Error in cleanup function:', error);
      }
    };
  }, []);

  // Send a message
  const sendMessage = useCallback(
    async (text: string): Promise<{ success: boolean; reason?: string }> => {
      if (!channel || !user || !text.trim()) {
        return { success: false, reason: 'missing_data' };
      }

      setIsSending(true);
      try {
        // Send message to GetStream only - no longer storing in Supabase
        const response = await channel.sendMessage({
          text,
          user_id: user.id,
        });

        if (process.env.NODE_ENV === 'development') {
    console.log('[useGetStreamChat] Message sent successfully:', response.message.id);
  }
        setIsSending(false);
        return { success: true };
      } catch (err) {
        console.error('[useGetStreamChat] Error sending message:', err);
        setIsSending(false);
        return {
          success: false,
          reason: err instanceof Error ? err.message : 'unknown_error'
        };
      }
    },
    [channel, user, taskId, threadId]
  );

  return {
    client,
    channel,
    messages,
    isLoading,
    isSending,
    error,
    sendMessage,
  };
}

export default useGetStreamChat;

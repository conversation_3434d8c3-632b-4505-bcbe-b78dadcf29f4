
import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { isPWA, isOnline, getCachedProfileData, storeProfileData, clearProfileCache } from '@/utils/pwa-utils';

export type ProfileData = {
  id: string;
  first_name: string | null;
  last_name: string | null;
  bio: string | null;
  avatar_url: string | null;
  account_type: string;
  username: string | null;
  created_at: string;
  organization_id: string | null;
  role: string | null;
  job_title: string | null;
  location: string | null;
};

export const useProfile = (userId: string | undefined) => {
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [lastSyncTime, setLastSyncTime] = useState<number>(0);

  // Function to fetch profile from Supabase
  const fetchProfileFromSupabase = useCallback(async (uid: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', uid)
        .single();

      if (error) throw error;

      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log("useProfile hook - fetched profile data from Supabase:", data);
  }
      }

      // Store in cache for PWA
      if (isPWA()) {
        storeProfileData(uid, data);
      }

      setProfile(data);
      setLastSyncTime(Date.now());
      return data;
    } catch (err: any) {
      console.error('Error fetching profile from Supabase:', err);
      throw err;
    }
  }, []);

  // Main fetch function that handles caching
  const fetchProfile = useCallback(async (uid: string) => {
    try {
      setIsLoading(true);
      setError(null); // Clear previous errors

      // For PWA in offline mode, try to use cached data
      if (isPWA() && !isOnline()) {
        const cachedProfile = getCachedProfileData(uid);
        if (cachedProfile) {
          if (process.env.NODE_ENV === 'development') {
    console.log("useProfile hook - using cached profile data:", cachedProfile);
  }
          setProfile(cachedProfile);
          setIsLoading(false);
          return;
        }
      }

      // For PWA in online mode, check if we have cached data first
      if (isPWA() && isOnline()) {
        const cachedProfile = getCachedProfileData(uid);
        if (cachedProfile) {
          // Use cached data immediately while fetching fresh data
          if (process.env.NODE_ENV === 'development') {
    console.log("useProfile hook - using cached profile data while fetching fresh data");
  }
          setProfile(cachedProfile);

          // Then fetch fresh data in the background
          fetchProfileFromSupabase(uid)
            .catch(err => {
              console.error('Background profile fetch failed:', err);
            });

          setIsLoading(false);
          return;
        }
      }

      // No cache or not PWA, fetch directly
      await fetchProfileFromSupabase(uid);
    } catch (err: any) {
      console.error('Error fetching profile:', err);
      setError(err);
      toast({
        variant: "destructive",
        title: "Error loading profile",
        description: err.message || "Could not load user profile",
      });
    } finally {
      setIsLoading(false);
    }
  }, [fetchProfileFromSupabase]);

  // Effect to fetch profile when userId changes
  useEffect(() => {
    if (!userId) {
      setIsLoading(false);
      setProfile(null); // Clear profile when userId is undefined/null
      return;
    }

    fetchProfile(userId);
  }, [userId, fetchProfile]);

  // Function to update profile
  const updateProfile = async (updates: Partial<ProfileData>) => {
    if (!userId) return { success: false, error: new Error('No user ID provided') };

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString() // Always update the timestamp
        })
        .eq('id', userId);

      if (error) throw error;

      // Update local state
      setProfile(prev => {
        const updated = prev ? { ...prev, ...updates, updated_at: new Date().toISOString() } : null;

        // Update cache for PWA
        if (isPWA() && updated) {
          storeProfileData(userId, updated);
        }

        return updated;
      });

      setLastSyncTime(Date.now());
      return { success: true, error: null };
    } catch (err: any) {
      console.error('Error updating profile:', err);
      return { success: false, error: err };
    }
  };

  // Function to force refresh profile data
  const refetch = useCallback(() => {
    if (userId) {
      return fetchProfile(userId);
    }
  }, [userId, fetchProfile]);

  // Function to clear profile cache
  const clearCache = useCallback(() => {
    if (userId && isPWA()) {
      clearProfileCache(userId);
    }
  }, [userId]);

  return {
    profile,
    isLoading,
    error,
    updateProfile,
    refetch,
    clearCache,
    lastSyncTime
  };
};

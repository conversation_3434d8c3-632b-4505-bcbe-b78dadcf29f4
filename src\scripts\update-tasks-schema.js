// Script to update the tasks table schema
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateTasksSchema() {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log('Updating tasks table schema...');
  }
    // First, check if the columns already exist
    const { data: existingData, error: checkError } = await supabase
      .from('tasks')
      .select('assigned_to, visibility')
      .limit(1);
    
    if (checkError) {
      // If the error is about missing columns, we need to add them
      if (checkError.message.includes('column "assigned_to" does not exist') || 
          checkError.message.includes('column "visibility" does not exist')) {
        if (process.env.NODE_ENV === 'development') {
    console.log('Columns do not exist, adding them...');
  }
      } else {
        console.error('Error checking existing columns:', checkError);
        return;
      }
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('Columns already exist:', existingData);
  }
      // If we got here, the columns already exist
      return;
    }
    
    // Execute SQL to add the columns
    // Note: We're using the SQL API directly since we need to alter the table
    const { error: sqlError } = await supabase.rpc('execute_sql', {
      sql: `
        -- Add assigned_to column
        ALTER TABLE public.tasks 
        ADD COLUMN IF NOT EXISTS assigned_to UUID REFERENCES auth.users(id);
        
        -- Add visibility column with default value 'public' for backward compatibility
        ALTER TABLE public.tasks 
        ADD COLUMN IF NOT EXISTS visibility TEXT NOT NULL DEFAULT 'public';
        
        -- Update existing tasks to have public visibility
        UPDATE public.tasks SET visibility = 'public' WHERE visibility IS NULL;
      `
    });
    
    if (sqlError) {
      console.error('Error executing SQL:', sqlError);
      
      // Try an alternative approach if the RPC method fails
      if (process.env.NODE_ENV === 'development') {
    console.log('Trying alternative approach...');
  }
      // Try to use a direct query to add the columns
      const { error: alterError } = await supabase.rpc('alter_table', {
        table_name: 'tasks',
        column_definitions: 'assigned_to UUID REFERENCES auth.users(id), visibility TEXT NOT NULL DEFAULT \'public\''
      });
      
      if (alterError) {
        console.error('Error with alternative approach:', alterError);
        return;
      }
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log('Successfully updated tasks table schema');
  }
    // Now update the RLS policies
    if (process.env.NODE_ENV === 'development') {
    console.log('Updating RLS policies...');
  }
    // First, drop existing policies
    const { error: dropError } = await supabase.rpc('execute_sql', {
      sql: `
        -- Drop existing task policies
        DROP POLICY IF EXISTS "Organization members can view their organization's tasks" ON public.tasks;
      `
    });
    
    if (dropError) {
      console.error('Error dropping existing policies:', dropError);
      return;
    }
    
    // Create new policies
    const { error: createPoliciesError } = await supabase.rpc('execute_sql', {
      sql: `
        -- Create new task visibility policies
        CREATE POLICY "Admins can view all organization tasks" 
        ON public.tasks
        FOR SELECT
        USING (
          EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role = 'admin'
            AND organization_id = (
              SELECT organization_id FROM public.profiles 
              WHERE id = (SELECT user_id FROM public.tasks WHERE id = public.tasks.id)
            )
          )
        );

        CREATE POLICY "Teachers can view their own tasks" 
        ON public.tasks
        FOR SELECT
        USING (
          user_id = auth.uid()
        );

        CREATE POLICY "Maintenance and support can view assigned tasks" 
        ON public.tasks
        FOR SELECT
        USING (
          (visibility = 'internal' OR visibility = 'public')
          AND assigned_to = auth.uid()
        );

        CREATE POLICY "Suppliers can view public tasks" 
        ON public.tasks
        FOR SELECT
        USING (
          visibility = 'public'
          AND EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND account_type = 'supplier'
          )
        );

        -- Create policies for task creation
        CREATE POLICY "Teachers and admins can create tasks" 
        ON public.tasks
        FOR INSERT
        WITH CHECK (
          EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND (role = 'teacher' OR role = 'admin')
          )
        );

        -- Create policies for task updates
        CREATE POLICY "Admins can update any task" 
        ON public.tasks
        FOR UPDATE
        USING (
          EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role = 'admin'
            AND organization_id = (
              SELECT organization_id FROM public.profiles 
              WHERE id = (SELECT user_id FROM public.tasks WHERE id = public.tasks.id)
            )
          )
        );

        CREATE POLICY "Task owners can update their own tasks" 
        ON public.tasks
        FOR UPDATE
        USING (
          user_id = auth.uid()
        );

        -- Create policies for task deletion
        CREATE POLICY "Admins can delete any task" 
        ON public.tasks
        FOR DELETE
        USING (
          EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role = 'admin'
            AND organization_id = (
              SELECT organization_id FROM public.profiles 
              WHERE id = (SELECT user_id FROM public.tasks WHERE id = public.tasks.id)
            )
          )
        );

        CREATE POLICY "Task owners can delete their own tasks" 
        ON public.tasks
        FOR DELETE
        USING (
          user_id = auth.uid()
        );
      `
    });
    
    if (createPoliciesError) {
      console.error('Error creating new policies:', createPoliciesError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log('Successfully updated RLS policies');
  }
  } catch (error) {
    console.error('Error updating tasks schema:', error);
  }
}

updateTasksSchema();

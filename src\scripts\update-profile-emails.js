// Script to update profile emails from auth.users
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateProfileEmails() {
  try {
    if (process.env.NODE_ENV === 'development') {
    console.log('Starting profile email update...');
  }
    // Get all profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*');
    
    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${profiles.length} total profiles`);
  }
    // Get all users from auth.users
    const { data: authData, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('Error fetching auth users:', authError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {

    
      console.log(`Found ${authData.users.length} total auth users`);
    

    
      }
    // Create a map of user IDs to emails
    const userEmailMap = {};
    authData.users.forEach(user => {
      userEmailMap[user.id] = user.email;
    });
    
    // Find profiles that need email updates
    const profilesToUpdate = profiles.filter(profile => {
      const authEmail = userEmailMap[profile.id];
      return authEmail && (!profile.email || profile.email !== authEmail);
    });
    
    if (process.env.NODE_ENV === 'development') {
    console.log(`Found ${profilesToUpdate.length} profiles that need email updates`);
  }
    // Update each profile
    for (const profile of profilesToUpdate) {
      const authEmail = userEmailMap[profile.id];
      
      if (process.env.NODE_ENV === 'development') {
    console.log(`Updating profile ${profile.id} with email ${authEmail}`);
  }
      const { data, error } = await supabase
        .from('profiles')
        .update({ email: authEmail })
        .eq('id', profile.id);
      
      if (error) {
        console.error(`Error updating profile ${profile.id}:`, error);
      } else {
        if (process.env.NODE_ENV === 'development') {
    console.log(`Successfully updated profile ${profile.id}`);
  }
      }
    }
    
    // Check if the profiles table has an email column
    if (process.env.NODE_ENV === 'development') {
    console.log('Checking if profiles table has email column...');
  }
    try {
      const { data: columnInfo, error: columnError } = await supabase.rpc('pgaudit.exec_sql', {
        sql_query: "SELECT column_name FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'email';"
      });
      
      if (columnError) {
        console.error('Error checking for email column:', columnError);
      } else {
        const hasEmailColumn = columnInfo && columnInfo.length > 0;
        if (process.env.NODE_ENV === 'development') {
    console.log(`Profiles table ${hasEmailColumn ? 'has' : 'does not have'} an email column`);
  }
        // If the email column doesn't exist, add it
        if (!hasEmailColumn) {
          if (process.env.NODE_ENV === 'development') {
    console.log('Adding email column to profiles table...');
  }
          const { error: alterError } = await supabase.rpc('pgaudit.exec_sql', {
            sql_query: "ALTER TABLE profiles ADD COLUMN IF NOT EXISTS email TEXT;"
          });
          
          if (alterError) {
            console.error('Error adding email column:', alterError);
          } else {
            if (process.env.NODE_ENV === 'development') {
    console.log('Successfully added email column to profiles table');
  }
            // Now update all profiles with emails from auth.users
            if (process.env.NODE_ENV === 'development') {

              console.log('Updating all profiles with emails from auth.users...');
            

              }
            for (const profile of profiles) {
              const authEmail = userEmailMap[profile.id];
              
              if (authEmail) {
                if (process.env.NODE_ENV === 'development') {
    console.log(`Updating profile ${profile.id} with email ${authEmail}`);
  }
                const { error: updateError } = await supabase
                  .from('profiles')
                  .update({ email: authEmail })
                  .eq('id', profile.id);
                
                if (updateError) {
                  console.error(`Error updating profile ${profile.id}:`, updateError);
                } else {
                  if (process.env.NODE_ENV === 'development') {
    console.log(`Successfully updated profile ${profile.id}`);
  }
                }
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error checking/adding email column:', error);
    }
    
    // Final verification
    const { data: updatedProfiles, error: verificationError } = await supabase
      .from('profiles')
      .select('*');
    
    if (verificationError) {
      console.error('Error fetching updated profiles:', verificationError);
    } else {
      if (process.env.NODE_ENV === 'development') {
    console.log('\nUpdated profiles:');
  }
      updatedProfiles.forEach(profile => {
        if (process.env.NODE_ENV === 'development') {
    console.log(`ID: ${profile.id}, Email: ${profile.email}, Organization: ${profile.organization_id}, Role: ${profile.role}`);
  }
      });
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log('\nProfile email update completed.');
  }
  } catch (error) {
    console.error('Unexpected error updating profile emails:', error);
  }
}

updateProfileEmails();

/**
 * StreamUIChatSimple Component
 *
 * A simplified version of the StreamUIChat component that doesn't import any CSS files
 * and uses only inline styles. This is to fix Vercel build issues.
 */

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { getStreamClient, connectUser } from '@/integrations/getstream/client';
import { getStreamApiUrl } from '@/utils/apiConfig';

// Import GetStream components
import {
  Chat,
  Channel,
  ChannelHeader,
  MessageInput,
  MessageList,
  Thread,
  Window,
  LoadingIndicator,
  useMessageContext,
  MessageSimple,
} from 'stream-chat-react';

// Custom SystemMessage component
const SystemMessage: React.FC = () => {
  const { message } = useMessageContext();

  // Check if this is a system message
  const isSystemMessage = message.type === 'system';

  if (!isSystemMessage) {
    // For regular messages, use the default MessageSimple component
    return <MessageSimple />;
  }

  return (
    <div className="str-chat__message str-chat__message--system">
      <div className="str-chat__message-text">
        <div className="str-chat__message-text-inner">
          {message.text}
        </div>
      </div>
    </div>
  );
};

interface StreamUIChatProps {
  taskId: string;
  taskTitle: string;
  threadId?: string;
  onSendMessage?: (message: string) => void;
  onSystemMessage?: (message: string) => void;
  className?: string;
}

export const StreamUIChatSimple: React.FC<StreamUIChatProps> = ({
  taskId,
  taskTitle,
  threadId,
  onSendMessage,
  onSystemMessage,
  className = '',
}) => {
  const { user, profile } = useAuth();
  const [channel, setChannel] = useState<any>(null);
  const [client, setClient] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Define the sendSystemMessage function
  const sendSystemMessage = async (text: string) => {
    if (!channel) return false;

    try {
      // Send a system message to the channel via the API route
      const response = await fetch(getStreamApiUrl('/system-message'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channelId: channel.id,
          text
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send system message via server');
      }

      // Call the onSystemMessage callback if provided
      if (onSystemMessage) {
        onSystemMessage(text);
      }

      if (process.env.NODE_ENV === 'development') {
    console.log('[StreamUIChat] System message sent via server:', text);
  }
      return true;
    } catch (error) {
      console.error('[StreamUIChat] Error sending system message:', error);
      return false;
    }
  };

  // Expose the sendSystemMessage function to the parent component
  useEffect(() => {
    if (channel && onSystemMessage) {
      // Make the sendSystemMessage function available to the parent component
      (window as any).sendStreamSystemMessage = sendSystemMessage;
    }

    return () => {
      // Clean up when the component unmounts
      if ((window as any).sendStreamSystemMessage === sendSystemMessage) {
        delete (window as any).sendStreamSystemMessage;
      }
    };
  }, [channel, onSystemMessage]);

  // Initialize the chat
  useEffect(() => {
    const initializeChat = async () => {
      if (!user || !taskId) return;

      try {
        setLoading(true);
        setError(null);

        // Get the user's name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        // Connect the user to Stream
        const streamClient = await connectUser(
          user.id,
          userName,
          user.id,
          profile?.avatar_url
        );

        // Set the client
        setClient(streamClient);

        // Get the task owner
        const { data: taskData, error: taskDataError } = await supabase
          .from('tasks')
          .select('user_id')
          .eq('id', taskId)
          .single();

        if (taskDataError) {
          throw new Error(`Error fetching task: ${taskDataError.message}`);
        }

        // Create an array of members including both the task owner and current user
        const members = [user.id];
        if (taskData?.user_id && taskData.user_id !== user.id) {
          members.push(taskData.user_id);
        }

        // Define the channel ID
        const channelId = `task-${taskId}`;

        try {
          // First try to get the channel if it exists
          try {
            // Try to get the channel with the client
            const taskChannel = streamClient.channel('messaging', channelId);
            await taskChannel.watch();

            // Channel exists, set it and we're done
            setChannel(taskChannel);
            setLoading(false);
            if (process.env.NODE_ENV === 'development') {
              console.log('[StreamUIChat] Successfully connected to existing channel:', channelId);
              }
          } catch (channelError) {
            // Channel doesn't exist or user doesn't have access, create it
            if (process.env.NODE_ENV === 'development') {
              console.log('[StreamUIChat] Channel not found or not accessible, creating new channel...');

              }
            // Create the channel using the API route
            const response = await fetch(getStreamApiUrl('/channels'), {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                taskId,
                taskTitle: taskTitle || 'Task Chat',
                members
              }),
            });

            if (!response.ok) {
              throw new Error('Failed to create channel via server');
            }

            const data = await response.json();
            if (process.env.NODE_ENV === 'development') {
              console.log('[StreamUIChat] Channel created successfully via server: completed');

              }
            // Now try to get the channel with the client again
            const taskChannel = streamClient.channel('messaging', channelId);
            await taskChannel.watch();

            setChannel(taskChannel);
            setLoading(false);
          }
        } catch (error) {
          console.error('[StreamUIChat] Error creating or accessing channel:', error);
          throw new Error('Failed to create or access channel');
        }
      } catch (err: any) {
        console.error('[StreamUIChat] Error initializing chat:', err);
        setError(err.message || 'Failed to initialize chat');
        setLoading(false);
      }
    };

    initializeChat();

    // Cleanup function
    return () => {
      if (client) {
        // Disconnect the user when the component unmounts
        const cleanup = async () => {
          try {
            await client.disconnectUser();
            if (process.env.NODE_ENV === 'development') {
              console.log('[StreamUIChat] Disconnected user from Stream');
              }
          } catch (err) {
            console.error('[StreamUIChat] Error disconnecting user:', err);
          }
        };

        cleanup();
      }
    };
  }, [user, taskId, profile]);

  // Handle sending messages
  const handleSendMessage = (message: any) => {
    if (onSendMessage) {
      onSendMessage(message.text);
    }
  };

  // Custom theme options - using GetStream's built-in themes
  const theme = 'messaging light';

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <LoadingIndicator size={32} />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 text-red-500 ${className}`}>
        <p>Error: {error}</p>
        <p className="mt-2">There was a problem connecting to the chat. Please try refreshing the page.</p>
      </div>
    );
  }

  if (!client || !channel) {
    return (
      <div className={`p-4 text-gray-500 ${className}`}>
        <p>Chat is initializing. Please wait a moment...</p>
      </div>
    );
  }

  // Define inline styles for system messages and basic GetStream styles
  const customStyles = `
    /* Basic GetStream styles */
    .str-chat {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    
    .str-chat__container {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    
    .str-chat__main-panel {
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
    }
    
    .str-chat__message-list {
      flex: 1;
      overflow-y: auto;
      padding: 10px;
    }
    
    .str-chat__message {
      margin-bottom: 10px;
    }
    
    .str-chat__input {
      padding: 10px;
      border-top: 1px solid #f0f0f0;
    }
    
    /* System message styles */
    .str-chat__message--system {
      margin: 10px auto;
      padding: 8px 16px;
      background-color: rgba(0, 119, 255, 0.05);
      border-radius: 16px;
      max-width: 80%;
      text-align: center;
    }
    
    .str-chat__message--system .str-chat__message-text {
      font-size: 0.85em;
      color: #0077ff;
      font-style: italic;
    }
    
    .str-chat__message-text-inner {
      max-width: 100%;
      word-break: break-word;
    }
  `;

  return (
    <div className={`str-chat-container ${className}`} style={{ height: '600px', maxHeight: '75vh' }}>
      {/* Add inline styles */}
      <style>{customStyles}</style>
      
      <Chat client={client} theme={theme}>
        <Channel channel={channel} Message={SystemMessage}>
          <Window>
            <ChannelHeader />
            <MessageList />
            <MessageInput onSubmit={handleSendMessage} />
          </Window>
          <Thread />
        </Channel>
      </Chat>
    </div>
  );
};

export default StreamUIChatSimple;

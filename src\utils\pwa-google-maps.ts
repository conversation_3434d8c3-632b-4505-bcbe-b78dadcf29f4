/**
 * Utility for loading and managing Google Maps API in the PWA
 * Implements a singleton pattern to ensure the API is loaded only once
 */

// Global state for Google Maps API loading
class GoogleMapsLoader {
  private static instance: GoogleMapsLoader;
  private isLoadingScript: boolean = false;
  private isScriptLoaded: boolean = false;
  private scriptLoadCallbacks: (() => void)[] = [];
  private loadPromise: Promise<void> | null = null;
  private globalCallbackName: string | null = null;

  // Private constructor to enforce singleton pattern
  private constructor() {}

  // Get the singleton instance
  public static getInstance(): GoogleMapsLoader {
    if (!GoogleMapsLoader.instance) {
      GoogleMapsLoader.instance = new GoogleMapsLoader();
    }
    return GoogleMapsLoader.instance;
  }

  /**
   * Load the Google Maps API and return a promise
   * @param libraries Optional libraries to load (default: 'places')
   * @returns Promise that resolves when the API is loaded
   */
  public loadApi(libraries: string = 'places'): Promise<void> {
    // If we already have a promise, return it
    if (this.loadPromise) {
      return this.loadPromise;
    }

    // Create a new promise for loading the API
    this.loadPromise = new Promise<void>((resolve, reject) => {
      // If the API is already loaded, resolve immediately
      if (this.isScriptLoaded && window.google && window.google.maps) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWA Google Maps] API already loaded');
          }
        resolve();
        return;
      }

      // Check if the script tag already exists in the document
      const existingScript = document.getElementById('google-maps-script');
      if (existingScript) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWA Google Maps] Script tag already exists in document');
          }
        // Add the resolve function to the callbacks
        this.scriptLoadCallbacks.push(resolve);
        return;
      }

      // If the script is currently loading, add the callback to the queue
      if (this.isLoadingScript) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWA Google Maps] API is loading, adding callback to queue');
          }
        this.scriptLoadCallbacks.push(resolve);
        return;
      }

      // Start loading the script
      this.isLoadingScript = true;
      this.scriptLoadCallbacks.push(resolve);

      if (process.env.NODE_ENV === 'development') {
    console.log('[PWA Google Maps] Starting to load API');
  }
      // Get API key from Vite's built-in environment variables - NO hardcoded fallback for security
      const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;

      if (process.env.NODE_ENV === 'development') {

        console.log('[PWA Google Maps] Using API key:', apiKey ? `${apiKey.substring(0, 8)}...` : 'none');


        }
      if (!apiKey) {
        console.error('[PWA Google Maps] API key is missing');
        // Resolve all callbacks even without loading the script
        this.scriptLoadCallbacks.forEach(cb => setTimeout(cb, 0));
        this.scriptLoadCallbacks = [];
        this.isLoadingScript = false;
        this.loadPromise = null;
        resolve(); // Resolve the promise even though loading failed
        return;
      }

      // Use a static callback name to avoid multiple definitions
      this.globalCallbackName = 'googleMapsInitCallback';

      // Create the callback function
      (window as any)[this.globalCallbackName] = () => {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWA Google Maps] API loaded successfully');
          }
        this.isScriptLoaded = true;

        // Call all queued callbacks
        this.scriptLoadCallbacks.forEach(cb => cb());
        this.scriptLoadCallbacks = [];
        this.isLoadingScript = false;
      };

      // Create the script element
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=${libraries}&callback=${this.globalCallbackName}`;
      script.async = true;
      script.defer = true;
      script.id = 'google-maps-script';

      // Handle script load errors
      script.onerror = (error) => {
        console.error('[PWA Google Maps] Failed to load API:', error);

        // Call all callbacks even if loading failed
        this.scriptLoadCallbacks.forEach(cb => cb());
        this.scriptLoadCallbacks = [];
        this.isLoadingScript = false;
        this.loadPromise = null;

        // Don't delete the callback in case of retry
        reject(new Error('Failed to load Google Maps API'));
      };

      // Set a timeout to handle cases where the callback is never called
      const timeoutId = setTimeout(() => {
        if (this.isLoadingScript) {
          console.warn('[PWA Google Maps] Loading timed out');

          // Call all callbacks even if loading timed out
          this.scriptLoadCallbacks.forEach(cb => cb());
          this.scriptLoadCallbacks = [];
          this.isLoadingScript = false;
          this.loadPromise = null;

          // Don't delete the callback in case of retry
          resolve(); // Resolve anyway to prevent hanging promises
        }
      }, 10000); // 10 second timeout

      // Add the script to the document
      document.head.appendChild(script);
    });

    return this.loadPromise;
  }

  /**
   * Check if the Google Maps API is loaded
   * @returns True if the API is loaded, false otherwise
   */
  public isLoaded(): boolean {
    return !!(window.google && window.google.maps);
  }
}

// Export a singleton instance of the loader
const googleMapsLoader = GoogleMapsLoader.getInstance();

/**
 * Load the Google Maps API with the specified libraries
 * @param callback Function to call when the API is loaded
 * @param libraries Optional libraries to load (default: 'places')
 */
export const loadGoogleMapsApi = (
  callback: () => void,
  libraries: string = 'places'
): void => {
  googleMapsLoader.loadApi(libraries)
    .then(callback)
    .catch(error => {
      console.error('[PWA Google Maps] Error loading API:', error);
      // Call the callback anyway to prevent hanging UI
      callback();
    });
};

/**
 * Check if the Google Maps API is loaded
 * @returns True if the API is loaded, false otherwise
 */
export const isGoogleMapsLoaded = (): boolean => {
  return googleMapsLoader.isLoaded();
};

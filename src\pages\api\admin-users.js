// API route for admin user management
import { createClient } from '@supabase/supabase-js';

export default async function handler(req, res) {
  if (process.env.NODE_ENV === 'development') {

    console.log('Admin users API route called');
  

    }
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  try {
    const { action, userId, page, pageSize } = req.body;
    
    // Initialize Supabase with service role key for admin access
    const supabaseAdmin = createClient(
      process.env.SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co',
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );
    
    // Verify the requesting user is an admin
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    // Check if user is admin
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();
    
    if (profileError || profile?.role !== 'admin') {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }
    
    // Handle different actions
    switch (action) {
      case 'listUsers':
        const { data, error } = await supabaseAdmin.auth.admin.listUsers({
          page: page || 1,
          perPage: pageSize || 20,
        });
        
        if (error) throw error;
        return res.status(200).json(data);
        
      case 'deleteUser':
        if (!userId) {
          return res.status(400).json({ error: 'User ID is required' });
        }
        
        const { error: deleteError } = await supabaseAdmin.auth.admin.deleteUser(userId);
        
        if (deleteError) throw deleteError;
        return res.status(200).json({ success: true });
        
      default:
        return res.status(400).json({ error: 'Invalid action' });
    }
  } catch (error) {
    console.error('Admin API error:', error);
    return res.status(500).json({ error: error.message });
  }
}

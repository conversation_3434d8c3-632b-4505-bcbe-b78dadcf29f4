/**
 * Enhanced GetStream Chat Component
 *
 * This component uses the Stream Chat React library to provide a more polished
 * chat interface for the GetStream integration.
 */

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import {
  getStreamClient,
  connectUser,
  disconnectUser,
  incrementConnectionCount,
  decrementConnectionCount
} from '@/integrations/getstream/client';
import {
  Chat,
  Channel,
  ChannelHeader,
  MessageInput,
  MessageList,
  Thread,
  Window,
  MessageSimple,
  Avatar,
  DateSeparator,
  TypingIndicator,
  EmojiPicker
} from 'stream-chat-react';

// Import custom theme CSS
import '@/styles/getstream-theme.css';

interface EnhancedGetStreamChatProps {
  taskId: string;
  taskTitle: string;
  threadId?: string;
  onSendMessage?: (message: string) => void;
  className?: string;
}

export const EnhancedGetStreamChat: React.FC<EnhancedGetStreamChatProps> = ({
  taskId,
  taskTitle,
  threadId,
  onSendMessage,
  className = '',
}) => {
  const { user, profile } = useAuth();
  const [channel, setChannel] = useState<any>(null);
  const [client, setClient] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize the chat
  useEffect(() => {
    const initializeChat = async () => {
      if (!user || !taskId) return;

      try {
        setLoading(true);
        setError(null);

        // Increment connection count
        incrementConnectionCount();

        // Get the user's name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        // Connect the user to Stream
        const streamClient = await connectUser(
          user.id,
          userName,
          user.id,
          profile?.avatar_url
        );

        // Set the client
        setClient(streamClient);

        // Check if the task has been migrated to GetStream
        const { data: task, error: taskError } = await supabase
          .from('tasks')
          .select('chat_migrated_to_stream, getstream_channel_id')
          .eq('id', taskId)
          .single();

        if (taskError) {
          throw new Error(`Error fetching task: ${taskError.message}`);
        }

        // If the task has been migrated, get the channel
        if (task.chat_migrated_to_stream) {
          const channelId = task.getstream_channel_id || `task-${taskId}`;
          const taskChannel = streamClient.channel('messaging', channelId);

          // Initialize the channel
          await taskChannel.watch();

          setChannel(taskChannel);
          setLoading(false);
        } else {
          throw new Error('Task chat has not been migrated to GetStream yet');
        }
      } catch (err: any) {
        console.error('[EnhancedGetStreamChat] Error initializing chat:', err);
        setError(err.message || 'Failed to initialize chat');
        setLoading(false);

        // Decrement connection count on error
        decrementConnectionCount(false);
      }
    };

    initializeChat();

    // Cleanup function
    return () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('[EnhancedGetStreamChat] Component unmounting');

        }
      // Decrement connection count and let the manager handle disconnection
      try {
        decrementConnectionCount(true);
      } catch (error) {
        console.warn('[EnhancedGetStreamChat] Error in cleanup function:', error);
      }
    };
  }, [user, taskId, profile]);

  // Handle sending messages
  const handleSendMessage = (message: any) => {
    if (onSendMessage) {
      onSendMessage(message.text);
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 text-red-500 ${className}`}>
        <p>Error: {error}</p>
        <p className="mt-2">Please try migrating the chat to GetStream first.</p>
      </div>
    );
  }

  if (!client || !channel) {
    return (
      <div className={`p-4 text-gray-500 ${className}`}>
        <p>Chat not available. Please try migrating the chat to GetStream first.</p>
      </div>
    );
  }

  // Custom components for enhanced styling
  const CustomAvatar = (props: any) => {
    return <Avatar {...props} size={36} />;
  };

  const CustomMessage = (props: any) => {
    return <MessageSimple {...props} />;
  };

  const CustomDateSeparator = (props: any) => {
    return <DateSeparator {...props} />;
  };

  const CustomTypingIndicator = (props: any) => {
    return <TypingIndicator {...props} />;
  };

  return (
    <div className={`getstream-chat-container ${className}`}>
      <Chat client={client} theme="messaging light">
        <Channel
          channel={channel}
          Avatar={CustomAvatar}
          Message={CustomMessage}
          DateSeparator={CustomDateSeparator}
          TypingIndicator={CustomTypingIndicator}
        >
          <Window>
            <ChannelHeader />
            <MessageList />
            <MessageInput onSubmit={handleSendMessage} />
          </Window>
          <Thread />
        </Channel>
      </Chat>
    </div>
  );
};

export default EnhancedGetStreamChat;

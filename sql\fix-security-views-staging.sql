-- STAGING: Fix Security Advisor Warnings by Removing Unused Views
-- This script safely removes the SECURITY DEFINER views that are not used by the application
-- Run this in STAGING first to test, then apply to production

-- ============================================================================
-- SECURITY ADVISOR FIX: Remove Unused SECURITY DEFINER Views
-- ============================================================================

-- These views are flagged by Supabase Security Advisor as potential security risks
-- Analysis shows they are not used by the application and can be safely removed

BEGIN;

-- 1. Remove security_dashboard_view (unused - app uses localStorage + Edge Functions)
DROP VIEW IF EXISTS public.security_dashboard_view CASCADE;
COMMENT ON TABLE public.security_events IS 'Security events table - accessed directly by Edge Functions, not through views';

-- 2. Remove public_profile_info (unused - app uses direct profile queries with RLS)
DROP VIEW IF EXISTS public.public_profile_info CASCADE;

-- 3. Remove public_tasks (unused - app uses direct task queries with RLS)
DROP VIEW IF EXISTS public.public_tasks CASCADE;

-- 4. Remove public_profiles (unused - app uses direct profile queries with RLS)
DROP VIEW IF EXISTS public.public_profiles CASCADE;

-- ============================================================================
-- VERIFY SECURITY IMPROVEMENTS
-- ============================================================================

-- Check that the underlying tables still have proper RLS policies
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = t.tablename) as policy_count
FROM pg_tables t
WHERE schemaname = 'public' 
AND tablename IN ('security_events', 'security_incidents', 'profiles', 'tasks', 'organizations')
ORDER BY tablename;

-- Verify that security functions still exist and work
SELECT 
    routine_name,
    routine_type,
    security_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('get_security_statistics', 'get_recent_security_events')
ORDER BY routine_name;

-- ============================================================================
-- SECURITY VERIFICATION QUERIES
-- ============================================================================

-- Test that security_events table is still accessible (should work)
SELECT COUNT(*) as total_security_events FROM public.security_events;

-- Test that security functions still work (should work)
SELECT get_security_statistics() as security_stats;

-- Test that profiles table RLS is working (should only show current user's data)
SELECT COUNT(*) as accessible_profiles FROM public.profiles;

-- Test that tasks table RLS is working (should only show user's organization tasks)
SELECT COUNT(*) as accessible_tasks FROM public.tasks;

-- ============================================================================
-- ROLLBACK PLAN (if needed)
-- ============================================================================

-- If anything breaks, you can recreate the views with this rollback script:
-- (Save this for emergency use only)

/*
ROLLBACK SCRIPT (DO NOT RUN UNLESS NEEDED):

-- Recreate security_dashboard_view
CREATE OR REPLACE VIEW public.security_dashboard_view AS
SELECT 
    se.id,
    se.type,
    se.severity,
    se.user_email,
    se.action,
    se.resource,
    se.ip_address,
    se.timestamp,
    CASE 
        WHEN se.severity = 'critical' THEN 1
        WHEN se.severity = 'high' THEN 2
        WHEN se.severity = 'medium' THEN 3
        WHEN se.severity = 'low' THEN 4
        ELSE 5
    END as severity_order
FROM public.security_events se
ORDER BY se.timestamp DESC;

-- Add proper RLS policy
CREATE POLICY "Site admins only can view security dashboard" ON public.security_dashboard_view
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.is_site_admin = true
        )
    );

ALTER VIEW public.security_dashboard_view ENABLE ROW LEVEL SECURITY;
GRANT SELECT ON public.security_dashboard_view TO authenticated;
*/

COMMIT;

-- ============================================================================
-- POST-EXECUTION CHECKLIST
-- ============================================================================

/*
After running this script, verify:

1. ✅ Supabase Security Advisor shows 0 warnings
2. ✅ Security Dashboard still works: https://classtasker.com/admin/security
3. ✅ Test Alert button still works
4. ✅ Simulate Breach button still works
5. ✅ All other application functionality unchanged
6. ✅ Edge Functions still process security events
7. ✅ Email alerts still sent for critical events

If any issues occur, run the ROLLBACK SCRIPT above.
*/

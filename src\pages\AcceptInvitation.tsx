import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import MainLayout from '@/components/layout/MainLayout';
import { useAuth } from '@/contexts/AuthContext';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/hooks/use-toast';
import { Loader2, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { supabaseAdmin } from '@/services/supabaseAdmin';
import InvitationSyncService from '@/services/invitationSyncService';

const AcceptInvitation = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  const { user, acceptInvitation } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [success, setSuccess] = useState<boolean | null>(null);
  const [invitedEmail, setInvitedEmail] = useState<string | null>(null);
  const [syncStatus, setSyncStatus] = useState<{
    isPending: boolean;
    isProcessing: boolean;
    isError: boolean;
    errorMessage?: string;
    retryCount: number;
  } | null>(null);
  const navigate = useNavigate();

  // Function to get invitation details from token using robust database lookup
  const getInvitationDetails = async () => {
    if (!token) return;

    try {
      if (process.env.NODE_ENV === 'development') {
    console.log('🔍 Looking up invitation details for token:', token);
  }
      // Use the new robust RPC function to get invitation details
      const { data: invitationData, error: invitationError } = await supabase.rpc('get_invitation_details', {
        token_param: token
      });

      if (invitationError) {
        console.error('❌ Error fetching invitation details:', invitationError);
        setInvitedEmail(null);
        return;
      }

      if (!invitationData || invitationData.length === 0) {
        console.error('❌ No invitation found for token:', token);
        setInvitedEmail(null);
        return;
      }

      const invitation = invitationData[0];
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
    console.log('✅ Found invitation details:', {
          email: invitation.email,
          organization: invitation.organization_name,
          role: invitation.role,
          status: invitation.status,
          isValid: invitation.is_valid
        });
  }
      }

      setInvitedEmail(invitation.email);

      // Store invitation details for later use
      localStorage.setItem('pendingInvitationEmail', invitation.email);
      localStorage.setItem('pendingInvitationOrgName', invitation.organization_name);
      localStorage.setItem('pendingInvitationRole', invitation.role);

    } catch (error) {
      console.error('💥 Error getting invitation details:', error);
      setInvitedEmail(null);
    }
  };

  useEffect(() => {
    // Check if we have a token
    if (token) {
      // Store the token in localStorage for the new confirmation page
      localStorage.setItem('pendingInvitationToken', token);

      // Get invitation details regardless of whether a user is logged in
      getInvitationDetails().then(() => {
        // Set loading to false after getting invitation details
        setIsLoading(false);

        // If we have the invited email, store it in localStorage
        if (invitedEmail) {
          localStorage.setItem('pendingInvitationEmail', invitedEmail);
        }

        // If the user is logged in, redirect to the new confirmation page after a short delay
        if (user) {
          setTimeout(() => {
            navigate('/invitation-confirmation');
          }, 1000);
        }
      });
    } else {
      setIsLoading(false);
      setSuccess(false);
    }
  }, [token, user, navigate, invitedEmail]);

  // Check invitation sync status periodically
  useEffect(() => {
    if (!token || !user || !success) return;

    // Check status immediately
    const checkStatus = () => {
      if (token && user) {
        const status = InvitationSyncService.getInvitationStatus(token, user.id);
        setSyncStatus(status);

        // If the invitation is no longer pending or processing, stop checking
        if (!status.isPending && !status.isProcessing) {
          return false;
        }
        return true;
      }
      return false;
    };

    // Initial check
    const shouldContinue = checkStatus();

    // Set up interval to check status
    let intervalId: number | null = null;
    if (shouldContinue) {
      intervalId = window.setInterval(() => {
        const shouldContinueChecking = checkStatus();
        if (!shouldContinueChecking && intervalId !== null) {
          window.clearInterval(intervalId);
        }
      }, 2000);
    }

    return () => {
      if (intervalId !== null) {
        window.clearInterval(intervalId);
      }
    };
  }, [token, user, success]);

  const handleAcceptInvitation = async () => {
    if (!token || !user) return;

    // Store the token and email in localStorage for the confirmation page
    localStorage.setItem('pendingInvitationToken', token);
    if (invitedEmail) {
      localStorage.setItem('pendingInvitationEmail', invitedEmail);
    }

    // Redirect to the new confirmation page
    navigate('/invitation-confirmation');
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="p-6 flex justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  if (!token) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Invalid Invitation</CardTitle>
            </CardHeader>
            <CardContent className="p-6 text-center">
              <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">
                The invitation link is invalid or missing a token.
              </p>
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button onClick={() => navigate('/')}>Return to Home</Button>
            </CardFooter>
          </Card>
        </div>
      </MainLayout>
    );
  }

  if (!user) {
    // Store the token and email in localStorage for the login/register flow
    if (token) {
      localStorage.setItem('pendingInvitationToken', token);
    }
    if (invitedEmail) {
      localStorage.setItem('pendingInvitationEmail', invitedEmail);
    }

    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Sign In Required</CardTitle>
            </CardHeader>
            <CardContent className="p-6 text-center">
              <p className="text-gray-600 mb-4">
                Please sign in or create an account to accept this invitation.
              </p>
              {invitedEmail ? (
                <p className="text-gray-600 mb-4">
                  This invitation was sent to: <strong>{invitedEmail}</strong>
                </p>
              ) : (
                <p className="text-gray-600 mb-4">
                  Please sign in with the email address this invitation was sent to.
                </p>
              )}
            </CardContent>
            <CardFooter className="flex justify-center space-x-4">
              <Button
                onClick={() => {
                  // Pass the invited email and token to the login page
                  if (invitedEmail) {
                    navigate(`/login?email=${encodeURIComponent(invitedEmail)}&token=${token}`);
                  } else {
                    navigate(`/login?token=${token}`);
                  }
                }}
              >
                Sign In
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  // Pass the invited email and token to the registration page
                  if (invitedEmail) {
                    navigate(`/register?email=${encodeURIComponent(invitedEmail)}&token=${token}`);
                  } else {
                    navigate(`/register?token=${token}`);
                  }
                }}
              >
                Create Account
              </Button>
            </CardFooter>
          </Card>
        </div>
      </MainLayout>
    );
  }

  if (success === true) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Invitation Accepted</CardTitle>
            </CardHeader>
            <CardContent className="p-6 text-center">
              {syncStatus?.isProcessing ? (
                <>
                  <RefreshCw className="h-16 w-16 text-blue-500 mx-auto mb-4 animate-spin" />
                  <p className="text-gray-600 mb-4">
                    Your invitation has been accepted and your profile is being updated...
                  </p>
                  <p className="text-gray-600">
                    This may take a few moments. You'll be redirected automatically when complete.
                  </p>
                </>
              ) : syncStatus?.isError ? (
                <>
                  <div className="flex items-center justify-center mb-4">
                    <CheckCircle className="h-16 w-16 text-green-500 mr-2" />
                    <XCircle className="h-10 w-10 text-amber-500" />
                  </div>
                  <p className="text-gray-600 mb-4">
                    Your invitation has been accepted, but there was an issue updating your profile.
                  </p>
                  <p className="text-gray-600 mb-4">
                    Error: {syncStatus.errorMessage || "Unknown error"}
                  </p>
                  <p className="text-gray-600">
                    Retry attempt {syncStatus.retryCount} of 5. You can continue to the dashboard.
                  </p>
                </>
              ) : (
                <>
                  <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                  <p className="text-gray-600 mb-4">
                    You have successfully joined the organization.
                  </p>
                  <p className="text-gray-600">
                    Redirecting to dashboard...
                  </p>
                </>
              )}
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button onClick={() => navigate('/dashboard')}>
                Go to Dashboard
              </Button>
            </CardFooter>
          </Card>
        </div>
      </MainLayout>
    );
  }

  if (success === false) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Invitation Error</CardTitle>
            </CardHeader>
            <CardContent className="p-6 text-center">
              <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">
                The invitation could not be accepted. It may be invalid or expired.
              </p>
            </CardContent>
            <CardFooter className="flex justify-center">
              <Button onClick={() => navigate('/')}>Return to Home</Button>
            </CardFooter>
          </Card>
        </div>
      </MainLayout>
    );
  }

  // Check if the logged-in user is the same as the invited user
  const isCorrectUser = user?.email?.toLowerCase() === invitedEmail?.toLowerCase();

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-center">Accept Invitation</CardTitle>
          </CardHeader>
          <CardContent className="p-6 text-center">
            {invitedEmail && (
              <p className="text-gray-600 mb-4">
                This invitation was sent to: <strong>{invitedEmail}</strong>
              </p>
            )}

            {!isCorrectUser && user && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                <p className="text-yellow-700 mb-2">
                  <strong>Warning:</strong> You are currently logged in as <strong>{user.email}</strong>, but this invitation was sent to <strong>{invitedEmail}</strong>.
                </p>
                <p className="text-yellow-700">
                  Please log out and sign in with the correct email address to accept this invitation.
                </p>
              </div>
            )}

            {isCorrectUser && (
              <p className="text-gray-600 mb-4">
                You've been invited to join an organization. Would you like to accept this invitation?
              </p>
            )}
          </CardContent>
          <CardFooter className="flex justify-center space-x-4">
            {isCorrectUser ? (
              <>
                <Button
                  onClick={handleAcceptInvitation}
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Accept Invitation'
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => navigate('/')}
                  disabled={isProcessing}
                >
                  Decline
                </Button>
              </>
            ) : (
              <>
                {user && (
                  <Button
                    onClick={() => {
                      // Sign out and redirect to login with the invited email
                      supabase.auth.signOut().then(() => {
                        if (invitedEmail) {
                          navigate(`/login?email=${encodeURIComponent(invitedEmail)}&token=${token}`);
                        } else {
                          navigate(`/login?token=${token}`);
                        }
                      });
                    }}
                  >
                    Sign Out
                  </Button>
                )}
                <Button
                  variant="outline"
                  onClick={() => navigate('/')}
                >
                  Return to Home
                </Button>
              </>
            )}
          </CardFooter>
        </Card>
      </div>
    </MainLayout>
  );
};

export default AcceptInvitation;

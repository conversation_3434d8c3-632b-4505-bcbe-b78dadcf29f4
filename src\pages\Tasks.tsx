
import { useState, useEffect, useRef, useMemo } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import TaskCard from '@/components/tasks/TaskCard';
import TaskFilter from '@/components/tasks/TaskFilter';
import { Button } from '@/components/ui/button';
import { Pagination } from '@/components/ui/pagination';
import { Grid2X2, List, Search, Map } from 'lucide-react';
import TasksMap from '@/components/tasks/TasksMap';
import { useTasks } from '@/hooks/use-tasks';
import { Skeleton } from '@/components/ui/skeleton';
import { Link } from 'react-router-dom';
import { isLocationWithinRadius, getCoordinates, clearLocationCaches } from '@/utils/location-utils';

const Tasks = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'map'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState<{
    searchTerm?: string;
    category?: string;
    location?: string;
    radius?: number;
    minBudget?: number;
    maxBudget?: number;
    status?: string;
  }>({});

  const { tasks, isLoadingTasks } = useTasks();

  const handleFilterChange = (newFilters: any) => {
    // Ensure radius is a number and has a reasonable value
    if (newFilters.radius) {
      // Convert to number if it's a string
      const radiusValue = typeof newFilters.radius === 'string'
        ? parseFloat(newFilters.radius)
        : newFilters.radius;

      // Ensure it's a valid number
      if (!isNaN(radiusValue)) {
        newFilters.radius = Math.max(1, radiusValue); // Minimum radius of 1 mile
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Setting radius to:', newFilters.radius);
  }
      } else {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Invalid radius value:', newFilters.radius);
  }
        newFilters.radius = 10; // Default to 10 miles if invalid
      }
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Applying filters:', newFilters);
  }
    setFilters(newFilters);
    setCurrentPage(1);
  };

  // State for location-filtered tasks
  const [locationFilteredTasks, setLocationFilteredTasks] = useState<any[]>([]);
  const [isLocationFiltering, setIsLocationFiltering] = useState(false);

  // Refs to track previous filter values
  const prevLocation = useRef<string | undefined>(undefined);
  const prevRadius = useRef<number | undefined>(undefined);

  // Apply non-location filters to tasks
  const nonLocationFilteredTasks = tasks?.filter(task => {
    // Filter by search term
    if (filters.searchTerm && !task.title.toLowerCase().includes(filters.searchTerm.toLowerCase()) &&
        !task.description.toLowerCase().includes(filters.searchTerm.toLowerCase())) {
      return false;
    }

    // Filter by category
    if (filters.category && task.category !== filters.category) {
      return false;
    }

    // Filter by budget range
    if (filters.minBudget !== undefined && task.budget < filters.minBudget) {
      return false;
    }

    if (filters.maxBudget !== undefined && task.budget > filters.maxBudget) {
      return false;
    }

    // Filter by status
    if (filters.status && task.status !== filters.status) {
      return false;
    }

    return true;
  }) || [];

  // Apply location filter asynchronously
  useEffect(() => {
    // Optimized location filtering function
    const applyLocationFilter = async () => {
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Starting location filtering with:',
        'location:', filters.location,
        'radius:', filters.radius,
        'tasks count:', nonLocationFilteredTasks.length);
  }
      // Only clear caches when location or radius changes, not on every render
      if (prevLocation.current !== filters.location || prevRadius.current !== filters.radius) {
        // Only clear caches when location or radius changes
        clearLocationCaches();
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Location caches cleared due to location/radius change');
  }
        // Update refs
        prevLocation.current = filters.location;
        prevRadius.current = filters.radius;
      }

      if (!filters.location || !filters.radius) {
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: No location filter applied, showing all tasks');
  }
        setLocationFilteredTasks(nonLocationFilteredTasks);
        setIsLocationFiltering(false);
        return;
      }

      setIsLocationFiltering(true);
      console.time('locationFiltering');

      try {
        // Get coordinates for the filter location - only do this once
        if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Getting coordinates for filter location:', filters.location);
  }
        const filterCoords = await getCoordinates(filters.location);
        if (process.env.NODE_ENV === 'development') {

          console.log('DEBUG: Filter coordinates:', JSON.stringify(filterCoords));


          }
        // CRITICAL: Verify we actually have valid coordinates
        if (!filterCoords || typeof filterCoords.lat !== 'number' || typeof filterCoords.lng !== 'number') {
          console.error('DEBUG: CRITICAL ERROR - Invalid filter coordinates:', filterCoords);
          alert('Error: Could not get valid coordinates for the location. Please try a different location.');
          setLocationFilteredTasks(nonLocationFilteredTasks);
          setIsLocationFiltering(false);
          return;
        }

        if (!filterCoords) {
          if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Could not get coordinates for filter location, showing all tasks');
  }
          // If we couldn't get coordinates, show all tasks
          setLocationFilteredTasks(nonLocationFilteredTasks);
          setIsLocationFiltering(false);
          return;
        }

        // Pre-process tasks to group them by location to reduce API calls
        const tasksByLocation: Record<string, any[]> = {};

        // Group tasks by location string or coordinates
        nonLocationFilteredTasks.forEach(task => {
          // Use coordinates if available for more accurate grouping
          if (task.location_lat && task.location_lng) {
            const locationKey = `${task.location_lat},${task.location_lng}`;

            if (!tasksByLocation[locationKey]) {
              tasksByLocation[locationKey] = [];
            }
            tasksByLocation[locationKey].push(task);
          } else {
            // For string locations, normalize to improve matching
            const locationKey = task.location.toLowerCase().trim();

            if (!tasksByLocation[locationKey]) {
              tasksByLocation[locationKey] = [];
            }
            tasksByLocation[locationKey].push(task);
          }
        });

        const uniqueLocations = Object.keys(tasksByLocation).length;
        if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Grouped ${nonLocationFilteredTasks.length} tasks into ${uniqueLocations} unique locations`);
  }
        // Process location groups in batches
        const filteredTasks = [];
        const locationEntries = Object.entries(tasksByLocation);
        const batchSize = 5; // Process fewer locations per batch, but each batch handles multiple tasks

        for (let i = 0; i < locationEntries.length; i += batchSize) {
          if (process.env.NODE_ENV === 'development') {

            console.log(`DEBUG: Processing location batch ${Math.floor(i/batchSize) + 1} of ${Math.ceil(locationEntries.length/batchSize)}`);

            }
          console.time(`location_batch_${i}`);

          const batch = locationEntries.slice(i, i + batchSize);

          // Process each location batch
          const batchResults = await Promise.all(
            batch.map(async ([locationKey, tasks]) => {
              // Parse location key back to coordinates or string
              let taskCoords;

              if (locationKey.includes(',') && locationKey.split(',').length === 2) {
                // It's a coordinate pair
                const lat = parseFloat(locationKey.split(',')[0]);
                const lng = parseFloat(locationKey.split(',')[1]);

                if (isNaN(lat) || isNaN(lng)) {
                  console.error(`DEBUG: CRITICAL ERROR - Invalid coordinates in key: ${locationKey}`);
                  // Skip this batch
                  return [];
                }

                taskCoords = { lat, lng };
                if (process.env.NODE_ENV === 'development') {

                  console.log(`DEBUG: Using coordinates for location: ${JSON.stringify(taskCoords)}`);

                  }
              } else {
                // It's a location string - use the original location from the first task
                // This preserves capitalization and formatting for better geocoding
                taskCoords = tasks[0].location;
                if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Using location string: ${taskCoords}`);
  }
              }

              if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Checking location: ${typeof taskCoords === 'string' ? taskCoords : 'coordinates object'} for ${tasks.length} tasks`);
  }
              // Check if this location is within radius - only one API call per unique location
              if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Checking if location is within radius ${filters.radius} miles`);
  }
              if (process.env.NODE_ENV === 'development') {

                console.log(`DEBUG: Filter coordinates: ${JSON.stringify(filterCoords)}`);

                }
              if (process.env.NODE_ENV === 'development') {

                console.log(`DEBUG: Task coordinates: ${typeof taskCoords === 'string' ? taskCoords : JSON.stringify(taskCoords)}`);


                }
              // We'll remove the special case for Farnborough and Farnham
              // and rely on the general distance calculation instead
              const isFarnboroughFarnhamCase = false;

              let isWithinRadius;

              if (isFarnboroughFarnhamCase && filters.radius >= 5) {
                if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: FORCE OVERRIDE - Farnborough and Farnham are within 5 miles of each other');
  }
                isWithinRadius = true;
              } else {
                isWithinRadius = await isLocationWithinRadius(
                  filterCoords,
                  taskCoords,
                  filters.radius
                );
              }

              if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Location is within radius: ${isWithinRadius}, affects ${tasks.length} tasks`);
  }
              // If within radius, return all tasks at this location, otherwise return empty array
              return isWithinRadius ? tasks : [];
            })
          );

          // Flatten and add all tasks from this batch
          const validResults = batchResults.flat();
          filteredTasks.push(...validResults);
          if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Batch added ${validResults.length} tasks to results`);
  }
          console.timeEnd(`location_batch_${i}`);

          // Allow UI to update between batches
          if (i + batchSize < locationEntries.length) {
            if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Yielding to UI thread before next batch');
  }
            await new Promise(resolve => setTimeout(resolve, 50)); // Longer timeout for better UI responsiveness
          }
        }

        if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Location filtering complete, found ${filteredTasks.length} tasks within radius`);
  }
        setLocationFilteredTasks(filteredTasks);
      } catch (error) {
        console.error('Error in location filtering:', error);
        // If there's an error, show all tasks
        setLocationFilteredTasks(nonLocationFilteredTasks);
      } finally {
        setIsLocationFiltering(false);
        console.timeEnd('locationFiltering');
      }
    };

    // Use a debounced version of the filter function to prevent excessive calls
    const timeoutId = setTimeout(() => {
      applyLocationFilter();
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [filters.location, filters.radius, nonLocationFilteredTasks]);

  // Use location-filtered tasks if location filter is applied, otherwise use non-location filtered tasks
  const filteredTasks = (filters.location && filters.radius) ? locationFilteredTasks : nonLocationFilteredTasks;

  // Pagination logic
  const itemsPerPage = 6;
  const totalPages = Math.ceil((filteredTasks.length || 0) / itemsPerPage);

  // Ensure currentPage is valid (not greater than totalPages)
  useEffect(() => {
    if (totalPages > 0 && currentPage > totalPages) {
      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Resetting currentPage from', currentPage, 'to', totalPages, 'because totalPages changed');
  }
      setCurrentPage(totalPages);
    }
  }, [totalPages, currentPage]);

  // Log pagination information
  if (process.env.NODE_ENV === 'development') {

    console.log('DEBUG: Pagination info:', {
    filteredTasksLength: filteredTasks.length,
    itemsPerPage,
    totalPages,
    currentPage,
    startIndex: (currentPage - 1) * itemsPerPage,
    endIndex: currentPage * itemsPerPage
  });


    }
  const paginatedTasks = filteredTasks.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Skeleton loader for tasks
  const TasksSkeletonLoader = () => (
    <div className={viewMode === 'grid'
      ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
      : "space-y-6"}>
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index} className="border rounded-lg p-4 shadow-sm">
          <div className="flex justify-between items-start mb-3">
            <Skeleton className="h-6 w-3/5 mb-2" />
            <Skeleton className="h-5 w-20 rounded-full" />
          </div>
          <Skeleton className="h-4 w-1/3 mb-3" />
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-5/6 mb-4" />
          <div className="flex justify-between mb-4">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-4 w-1/4" />
          </div>
          <div className="pt-3 border-t">
            <div className="flex justify-between">
              <Skeleton className="h-5 w-16" />
              <Skeleton className="h-4 w-12" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-3">Available Tasks</h1>
          <p className="text-gray-600 text-lg">Find tasks that match your skills and availability.</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-1">
            <TaskFilter onFilter={handleFilterChange} />
          </div>

          <div className="lg:col-span-3">
            <div className="mb-6 flex justify-between items-center bg-gray-50 p-4 rounded-lg border">
              <div className="flex items-center">
                {isLocationFiltering ? (
                  <div className="flex items-center">
                    <div className="animate-spin h-4 w-4 border-2 border-classtasker-blue border-opacity-50 border-t-transparent rounded-full mr-2"></div>
                    <p className="text-gray-700 font-medium">Filtering by location...</p>
                  </div>
                ) : (
                  <p className="text-gray-700 font-medium">
                    {filteredTasks.length} {filteredTasks.length === 1 ? 'task' : 'tasks'} found
                    {filters.location && filters.radius && (
                      <span className="ml-1 text-sm text-gray-500">
                        within {filters.radius} miles of {filters.location}
                      </span>
                    )}
                  </p>
                )}
              </div>
              <div className="flex space-x-2 bg-white rounded-md border p-1">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setViewMode('grid')}
                  className={viewMode === 'grid' ? 'bg-gray-100' : ''}
                >
                  <Grid2X2 size={18} className={viewMode === 'grid' ? 'text-classtasker-blue' : 'text-gray-500'} />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setViewMode('list')}
                  className={viewMode === 'list' ? 'bg-gray-100' : ''}
                >
                  <List size={18} className={viewMode === 'list' ? 'text-classtasker-blue' : 'text-gray-500'} />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setViewMode('map')}
                  className={viewMode === 'map' ? 'bg-gray-100' : ''}
                >
                  <Map size={18} className={viewMode === 'map' ? 'text-classtasker-blue' : 'text-gray-500'} />
                </Button>
              </div>
            </div>

            {isLoadingTasks || isLocationFiltering ? (
              <TasksSkeletonLoader />
            ) : paginatedTasks.length > 0 ? (
              viewMode === 'map' ? (
                // Map view
                <div className="mb-6">
                  {/* Pass ALL filtered tasks to the map, not just paginated ones */}
                  <TasksMap
                    tasks={filteredTasks}
                    filterLocation={filters.location} // Pass the filter location string
                    centerLocation={null} // Will be determined by the component
                    radius={filters.radius}
                  />

                  {/* Show a list of tasks below the map */}
                  <div className="mt-6 space-y-4">
                    <h3 className="text-lg font-medium">Task List ({filteredTasks.length} tasks)</h3>
                    <div className="space-y-2 max-h-[300px] overflow-y-auto p-2 border rounded-lg">
                      {/* Show all filtered tasks in the list, not just paginated ones */}
                      {filteredTasks.map(task => (
                        <div key={task.id} className="p-3 border rounded-lg hover:bg-gray-50">
                          <Link to={`/tasks/enhanced/${task.id}?messages=true`} className="block">
                            <h4 className="font-medium">{task.title}</h4>
                            <div className="flex justify-between mt-1">
                              <span className="text-sm text-gray-600">{task.location}</span>
                              <span className="text-sm font-medium">£{task.budget}</span>
                            </div>
                          </Link>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                // Grid or List view
                <div className={viewMode === 'grid'
                  ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
                  : "space-y-6"
                }>
                  {paginatedTasks.map(task => (
                    <Link
                      key={task.id}
                      to={`/tasks/enhanced/${task.id}?messages=true`}
                      className="block transition-transform hover:scale-[1.01] duration-200"
                    >
                      <TaskCard
                        id={task.id}
                        title={task.title}
                        description={task.description}
                        location={task.location}
                        location_formatted={task.location_formatted}
                        location_lat={task.location_lat}
                        location_lng={task.location_lng}
                        location_place_id={task.location_place_id}
                        dueDate={task.due_date}
                        budget={task.budget}
                        category={task.category}
                        status={task.status}
                        offers={task.offers_count}
                        visibility={task.visibility}
                        fullTask={task}
                      />
                    </Link>
                  ))}
                </div>
              )
            ) : (
              <div className="text-center py-16 bg-gray-50 rounded-lg border p-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                  <Search className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="text-xl font-medium text-gray-800 mb-2">No tasks found</h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">We couldn't find any tasks matching your current filters. Try adjusting your search criteria.</p>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => handleFilterChange({})}
                  className="bg-white hover:bg-gray-50"
                >
                  Clear All Filters
                </Button>
              </div>
            )}

            {/* Always show pagination if there are tasks */}
            {filteredTasks.length > 0 && (
              <div className="mt-10 flex justify-center">
                <div className="bg-white rounded-lg shadow-sm border p-2">
                  <Pagination
                    total={filteredTasks.length}
                    perPage={itemsPerPage}
                    currentPage={currentPage}
                    onPageChange={(page) => {
                      if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Page change requested to', page);
  }
                      setCurrentPage(page);
                    }}
                  />
                </div>
                <div className="ml-4 flex items-center text-sm text-gray-500">
                  Showing {paginatedTasks.length} of {filteredTasks.length} tasks
                  (Page {currentPage} of {totalPages})
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default Tasks;

/**
 * Utility function to load the Google Maps JavaScript API
 * @param apiKey Google Maps API key (optional, will use environment variable if not provided)
 * @returns Promise that resolves when the API is loaded
 */
export const loadGoogleMapsApi = (apiKey?: string): Promise<void> => {
  // Use provided API key or get from Vite's built-in environment variables
  const key = apiKey || import.meta.env.VITE_GOOGLE_MAPS_API_KEY || '';

  if (!key) {
    console.error('Google Maps API key not found');
    return Promise.reject(new Error('Google Maps API key not found'));
  }
  return new Promise((resolve, reject) => {
    // Check if the API is already loaded
    if (window.google && window.google.maps) {
      if (process.env.NODE_ENV === 'development') {
    console.log('Google Maps API already loaded');
  }
      resolve();
      return;
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('Loading Google Maps API...');
  }
    // Create a callback function that will be called when the API is loaded
    const callbackName = 'googleMapsApiLoaded';

    // Add the callback to the window object
    (window as any)[callbackName] = () => {
      if (process.env.NODE_ENV === 'development') {
    console.log('Google Maps API loaded successfully');
  }
      resolve();
      // Clean up
      delete (window as any)[callbackName];
    };

    // Create a script element to load the API
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${key}&libraries=places&callback=${callbackName}`;
    script.async = true;
    script.defer = true;
    script.onerror = (error) => {
      console.error('Error loading Google Maps API:', error);
      reject(new Error('Failed to load Google Maps API'));
    };

    // Add the script to the document
    document.head.appendChild(script);
  });
};

/**
 * Check if the Google Maps API is loaded
 * @returns True if the API is loaded, false otherwise
 */
export const isGoogleMapsLoaded = (): boolean => {
  return !!(window.google && window.google.maps);
};
/**
 * GetStream Channel Utility
 *
 * This utility provides functions for getting and creating GetStream channels for tasks.
 */

import { Channel, StreamChat } from 'stream-chat';
import {
  getStreamClient,
  getTaskChannel,
  createOrUpdateTaskChannel,
  ensureTaskParticipantsInChannel
} from '@/integrations/getstream/client';

/**
 * Get the GetStream channel for a task
 * @param taskId The ID of the task
 * @returns A promise that resolves to the GetStream channel
 */
export const getStreamChannelForTask = async (taskId: string): Promise<Channel | null> => {
  try {
    // Use the getTaskChannel function from client.ts
    return await getTaskChannel(taskId);
  } catch (error) {
    console.error('[getStreamChannel] Error getting channel for task:', error);
    return null;
  }
};

/**
 * Get all channels for a user
 * @param userId The user ID
 * @param client Optional StreamChat client (will create one if not provided)
 * @returns Array of channels
 */
export const getUserChannels = async (userId: string, client?: StreamChat): Promise<Channel[]> => {
  try {
    // Use the provided client or create a new one
    const streamClient = client || getStreamClient();

    // Make sure the client is connected
    if (!streamClient.userID) {
      if (process.env.NODE_ENV === 'development') {
        console.log('[getUserChannels] Client not connected, connecting user: completed');
        }
      await streamClient.connectUser(
        {
          id: userId,
          name: userId,
        },
        streamClient.devToken(userId)
      );
    }

    // Query channels where the user is a member
    const filter = { type: 'messaging', members: { $in: [userId] } };
    const sort = { last_message_at: -1 };

    const response = await streamClient.queryChannels(filter, sort, {
      watch: true,
      state: true,
      presence: true,
      limit: 30,
    });

    if (process.env.NODE_ENV === 'development') {

      console.log(`[getUserChannels] Found ${response.length} channels for user ${userId}`);

      }
    return response;
  } catch (error) {
    console.error('[getUserChannels] Error getting user channels:', error);
    return [];
  }
};

/**
 * Create a GetStream channel for a task
 * @param taskId The ID of the task
 * @param taskTitle The title of the task
 * @param members Array of user IDs to add as members
 * @returns A promise that resolves to the GetStream channel
 */
export const createStreamChannelForTask = async (
  taskId: string,
  taskTitle: string,
  members: string[] = []
): Promise<Channel | null> => {
  try {
    // Use the createOrUpdateTaskChannel function from client.ts
    return await createOrUpdateTaskChannel(
      taskId,
      taskTitle || 'Task Chat',
      members,
      true // Force update
    );
  } catch (error) {
    console.error('[getStreamChannel] Error creating channel for task:', error);
    return null;
  }
};

/**
 * Ensure all users involved in a task are added to the chat channel
 * This is a convenience wrapper around the function in client.ts
 * @param taskId The ID of the task
 * @returns The updated channel
 */
export const ensureAllTaskParticipantsInChannel = async (taskId: string): Promise<Channel | null> => {
  return await ensureTaskParticipantsInChannel(taskId);
};

export default {
  getStreamChannelForTask,
  createStreamChannelForTask,
  getUserChannels,
  ensureAllTaskParticipantsInChannel
};

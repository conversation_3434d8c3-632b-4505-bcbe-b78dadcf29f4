// <PERSON>ript to update the organizations table with improved location data
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateOrganizationLocationSchema() {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('Updating organizations table with location schema...');
    
      }
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../../sql/update_organization_location_schema.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error('Error executing SQL:', error);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log('Successfully updated organizations table schema with location fields');
  }
    // Verify the schema update
    const { data: columns, error: columnsError } = await supabase
      .rpc('get_table_columns', { table_name: 'organizations' });
    
    if (columnsError) {
      console.error('Error getting table columns:', columnsError);
      return;
    }
    
    if (process.env.NODE_ENV === 'development') {
    console.log('\nOrganizations table columns:');
  }
    columns.forEach(col => {
      if (process.env.NODE_ENV === 'development') {
        console.log(`- ${col.column_name} (${col.data_type}${col.is_nullable === 'NO' ? ', NOT NULL' : ''})`);
        }
    });
    
    // Check if the calculate_distance function exists
    const { data: functions, error: functionsError } = await supabase
      .rpc('get_functions');
    
    if (functionsError) {
      console.error('Error getting functions:', functionsError);
      return;
    }
    
    const hasCalculateDistance = functions.some(fn => fn.function_name === 'calculate_distance');
    if (process.env.NODE_ENV === 'development') {
      console.log(`\nCalculate distance function exists: ${hasCalculateDistance}`);
    
      }
    if (process.env.NODE_ENV === 'development') {
    console.log('\nSchema update completed successfully');
  }
  } catch (error) {
    console.error('Error updating schema:', error);
  }
}

// Run the function
updateOrganizationLocationSchema();

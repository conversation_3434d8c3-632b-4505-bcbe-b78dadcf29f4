/**
 * Optimized Mobile Chat List Component
 * 
 * A simplified, optimized implementation of the mobile chat list
 * that uses GetStream for chat functionality.
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { MessageSquare, AlertCircle } from 'lucide-react';

// Chat thread interface
interface ChatThread {
  id: string;
  task_id: string;
  status: string;
  created_at: string;
  updated_at: string;
  last_message_at: string;
  has_offer: boolean;
  is_closed: boolean;
  task_title: string;
  task_status: string;
  last_message?: string;
  last_message_time?: string;
  unread_count: number;
}

const OptimizedChatList: React.FC = () => {
  const [chatThreads, setChatThreads] = useState<ChatThread[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const navigate = useNavigate();

  // Fetch chat threads with a more efficient query
  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }

    if (process.env.NODE_ENV === 'development') {


      console.log('OptimizedChatList: Fetching chat threads for user: completed');
    


      }
    const fetchChatThreads = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch chat threads where the user is a participant
        const { data, error } = await supabase
          .from('chat_threads')
          .select(`
            id,
            task_id,
            status,
            created_at,
            updated_at,
            last_message_at,
            has_offer,
            is_closed,
            task:tasks!inner(title, status)
          `)
          .or(`supplier_id.eq.${user.id},admin_id.eq.${user.id}`)
          .order('last_message_at', { ascending: false });

        if (error) {
          console.error('Error fetching chat threads:', error);
          throw new Error(`Failed to fetch threads: ${error.message}`);
        }

        // If no threads, return empty array
        if (!data || data.length === 0) {
          setChatThreads([]);
          setLoading(false);
          return;
        }

        // Get last messages and unread counts in a single batch query
        const threadIds = data.map(thread => thread.id);
        
        // Get last messages for all threads
        const { data: lastMessages, error: messagesError } = await supabase
          .from('task_messages')
          .select('thread_id, content, created_at')
          .in('thread_id', threadIds)
          .order('created_at', { ascending: false });
          
        if (messagesError) {
          console.error('Error fetching last messages:', messagesError);
        }
        
        // Create a map of thread ID to last message
        const lastMessageMap: Record<string, { content: string, created_at: string }> = {};
        if (lastMessages) {
          for (const message of lastMessages) {
            if (!lastMessageMap[message.thread_id]) {
              lastMessageMap[message.thread_id] = {
                content: message.content,
                created_at: message.created_at
              };
            }
          }
        }
        
        // Format the threads with the last message data
        const formattedThreads = data.map(thread => ({
          id: thread.id,
          task_id: thread.task_id,
          status: thread.status,
          created_at: thread.created_at,
          updated_at: thread.updated_at,
          last_message_at: thread.last_message_at,
          has_offer: thread.has_offer,
          is_closed: thread.is_closed,
          task_title: thread.task.title,
          task_status: thread.task.status,
          last_message: lastMessageMap[thread.id]?.content,
          last_message_time: lastMessageMap[thread.id]?.created_at,
          unread_count: 0 // We'll set this to 0 for now for simplicity
        }));

        if (process.env.NODE_ENV === 'development') {
    console.log('Fetched chat threads:', formattedThreads.length);
  }
        setChatThreads(formattedThreads);
      } catch (err) {
        console.error('Error in fetchChatThreads:', err);
        setError(`Failed to load chat threads: ${err instanceof Error ? err.message : 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };

    fetchChatThreads();
  }, [user]);

  // Navigate to chat view
  const handleChatClick = (thread: ChatThread) => {
    if (!thread.id || !thread.task_id) {
      console.error('Missing thread ID or task ID', thread);
      return;
    }

    navigate(`/mobile/chat/${thread.id}?task=${thread.task_id}`);
  };

  // Format time display
  const formatTimeAgo = (dateString: string) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return format(date, 'HH:mm'); // Today, show time
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return format(date, 'EEEE'); // Day of week
    } else {
      return format(date, 'dd/MM/yyyy'); // Full date
    }
  };

  // Truncate message content
  const truncateMessage = (content: string, maxLength: number = 60) => {
    if (!content) return '';
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-4 py-3">
        <h1 className="text-xl font-semibold">Messages</h1>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          // Loading skeletons
          <div className="p-4 space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4 p-3 bg-white rounded-lg">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          // Error state
          <div className="p-8 text-center">
            <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <p className="text-red-500 font-medium mb-2">Error Loading Messages</p>
            <p className="text-gray-600 text-sm mb-4">{error}</p>
            <button
              onClick={() => setLoading(true)}
              className="mt-2 px-4 py-2 bg-blue-500 text-white rounded-md"
            >
              Try Again
            </button>
          </div>
        ) : chatThreads.length === 0 ? (
          // Empty state
          <div className="p-8 text-center">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No messages yet</p>
            <p className="text-sm text-gray-400 mt-2">
              When you have conversations about tasks, they'll appear here
            </p>
          </div>
        ) : (
          // Chat threads list
          <div className="divide-y divide-gray-100">
            {chatThreads.map((thread) => (
              <div
                key={thread.id}
                className={`w-full text-left p-4 ${thread.is_closed ? 'bg-gray-50' : 'bg-white'} hover:bg-gray-50 transition-colors`}
                onClick={() => handleChatClick(thread)}
              >
                <div className="flex items-center space-x-3">
                  <Avatar className="h-12 w-12 border border-gray-200">
                    <AvatarFallback className="bg-blue-100 text-blue-600">
                      {thread.task_title?.substring(0, 2).toUpperCase() || 'T'}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium text-gray-900 truncate pr-2">
                        {thread.task_title || 'Task'}
                      </h3>
                      <span className="text-xs text-gray-500 whitespace-nowrap">
                        {formatTimeAgo(thread.last_message_time || thread.updated_at)}
                      </span>
                    </div>

                    <div className="flex justify-between items-center mt-1">
                      <p className="text-sm truncate text-gray-500">
                        {thread.last_message ? truncateMessage(thread.last_message) : 'No messages yet'}
                      </p>

                      {thread.unread_count > 0 && (
                        <Badge variant="destructive" className="ml-2 px-1.5 py-0.5 text-xs">
                          {thread.unread_count}
                        </Badge>
                      )}
                    </div>

                    <div className="mt-1 flex items-center space-x-2">
                      <span className={`text-xs px-2 py-0.5 rounded-full ${
                        thread.status === 'interest' ? 'bg-blue-100 text-blue-700' :
                        thread.status === 'questions' ? 'bg-purple-100 text-purple-700' :
                        thread.status === 'offer' ? 'bg-green-100 text-green-700' :
                        'bg-gray-100 text-gray-700'
                      }`}>
                        {thread.status.charAt(0).toUpperCase() + thread.status.slice(1)}
                      </span>

                      {thread.has_offer && (
                        <span className="text-xs px-2 py-0.5 bg-green-100 text-green-700 rounded-full">
                          Offer
                        </span>
                      )}

                      {thread.is_closed && (
                        <span className="text-xs px-2 py-0.5 bg-gray-100 text-gray-700 rounded-full">
                          Closed
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default OptimizedChatList;

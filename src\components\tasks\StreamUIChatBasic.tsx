/**
 * StreamUIChatBasic Component
 *
 * A basic version of the StreamUIChat component that doesn't import any CSS files
 * and doesn't use any GetStream components. This is to fix Vercel build issues.
 */

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { getStreamClient, connectUser } from '@/integrations/getstream/client';
import { getStreamApiUrl } from '@/utils/apiConfig';

interface StreamUIChatBasicProps {
  taskId: string;
  taskTitle: string;
  threadId?: string;
  onSendMessage?: (message: string) => void;
  onSystemMessage?: (message: string) => void;
  className?: string;
}

export const StreamUIChatBasic: React.FC<StreamUIChatBasicProps> = ({
  taskId,
  taskTitle,
  threadId,
  onSendMessage,
  onSystemMessage,
  className = '',
}) => {
  const { user, profile } = useAuth();
  const [channel, setChannel] = useState<any>(null);
  const [client, setClient] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [messages, setMessages] = useState<any[]>([]);
  const [newMessage, setNewMessage] = useState('');

  // Define the sendSystemMessage function
  const sendSystemMessage = async (text: string) => {
    if (!channel) return false;

    try {
      // Send a system message to the channel via the API route
      const response = await fetch(getStreamApiUrl('/system-message'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channelId: channel.id,
          text
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send system message via server');
      }

      // Call the onSystemMessage callback if provided
      if (onSystemMessage) {
        onSystemMessage(text);
      }

      if (process.env.NODE_ENV === 'development') {
    console.log('[StreamUIChat] System message sent via server:', text);
  }
      return true;
    } catch (error) {
      console.error('[StreamUIChat] Error sending system message:', error);
      return false;
    }
  };

  // Expose the sendSystemMessage function to the parent component
  useEffect(() => {
    if (channel && onSystemMessage) {
      // Make the sendSystemMessage function available to the parent component
      (window as any).sendStreamSystemMessage = sendSystemMessage;
    }

    return () => {
      // Clean up when the component unmounts
      if ((window as any).sendStreamSystemMessage === sendSystemMessage) {
        delete (window as any).sendStreamSystemMessage;
      }
    };
  }, [channel, onSystemMessage]);

  // Initialize the chat
  useEffect(() => {
    const initializeChat = async () => {
      if (!user || !taskId) return;

      try {
        setLoading(true);
        setError(null);

        // Get the user's name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        // Connect the user to Stream
        const streamClient = await connectUser(
          user.id,
          userName,
          user.id,
          profile?.avatar_url
        );

        // Set the client
        setClient(streamClient);

        // Get the task owner
        const { data: taskData, error: taskDataError } = await supabase
          .from('tasks')
          .select('user_id')
          .eq('id', taskId)
          .single();

        if (taskDataError) {
          throw new Error(`Error fetching task: ${taskDataError.message}`);
        }

        // Create an array of members including both the task owner and current user
        const members = [user.id];
        if (taskData?.user_id && taskData.user_id !== user.id) {
          members.push(taskData.user_id);
        }

        // Define the channel ID
        const channelId = `task-${taskId}`;

        try {
          // First try to get the channel if it exists
          try {
            // Try to get the channel with the client
            const taskChannel = streamClient.channel('messaging', channelId);
            await taskChannel.watch();

            // Channel exists, set it and we're done
            setChannel(taskChannel);
            setLoading(false);
            if (process.env.NODE_ENV === 'development') {
              console.log('[StreamUIChat] Successfully connected to existing channel:', channelId);
              }
          } catch (channelError) {
            // Channel doesn't exist or user doesn't have access, create it
            if (process.env.NODE_ENV === 'development') {
              console.log('[StreamUIChat] Channel not found or not accessible, creating new channel...');

              }
            // Create the channel using the API route
            const response = await fetch(getStreamApiUrl('/channels'), {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                taskId,
                taskTitle: taskTitle || 'Task Chat',
                members
              }),
            });

            if (!response.ok) {
              throw new Error('Failed to create channel via server');
            }

            const data = await response.json();
            if (process.env.NODE_ENV === 'development') {
              console.log('[StreamUIChat] Channel created successfully via server: completed');

              }
            // Now try to get the channel with the client again
            const taskChannel = streamClient.channel('messaging', channelId);
            await taskChannel.watch();

            setChannel(taskChannel);
            setLoading(false);
          }
        } catch (error) {
          console.error('[StreamUIChat] Error creating or accessing channel:', error);
          throw new Error('Failed to create or access channel');
        }
      } catch (err: any) {
        console.error('[StreamUIChat] Error initializing chat:', err);
        setError(err.message || 'Failed to initialize chat');
        setLoading(false);
      }
    };

    initializeChat();

    // Cleanup function
    return () => {
      if (client) {
        // Disconnect the user when the component unmounts
        const cleanup = async () => {
          try {
            await client.disconnectUser();
            if (process.env.NODE_ENV === 'development') {
              console.log('[StreamUIChat] Disconnected user from Stream');
              }
          } catch (err) {
            console.error('[StreamUIChat] Error disconnecting user:', err);
          }
        };

        cleanup();
      }
    };
  }, [user, taskId, profile]);

  // Listen for new messages
  useEffect(() => {
    if (!channel) return;

    // Get the initial messages
    const getMessages = async () => {
      try {
        const response = await channel.query({ messages: { limit: 50 } });
        setMessages(response.messages || []);
      } catch (error) {
        console.error('[StreamUIChat] Error getting messages:', error);
      }
    };

    getMessages();

    // Listen for new messages
    const handleNewMessage = (event: any) => {
      setMessages((prevMessages) => [...prevMessages, event.message]);
    };

    channel.on('message.new', handleNewMessage);

    return () => {
      channel.off('message.new', handleNewMessage);
    };
  }, [channel]);

  // Handle sending messages
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim() || !channel) return;

    try {
      await channel.sendMessage({
        text: newMessage,
      });

      setNewMessage('');

      if (onSendMessage) {
        onSendMessage(newMessage);
      }
    } catch (error) {
      console.error('[StreamUIChat] Error sending message:', error);
    }
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 text-red-500 ${className}`}>
        <p>Error: {error}</p>
        <p className="mt-2">There was a problem connecting to the chat. Please try refreshing the page.</p>
      </div>
    );
  }

  if (!client || !channel) {
    return (
      <div className={`p-4 text-gray-500 ${className}`}>
        <p>Chat is initializing. Please wait a moment...</p>
      </div>
    );
  }

  return (
    <div className={`border rounded-lg overflow-hidden ${className}`} style={{ height: '600px', maxHeight: '75vh' }}>
      {/* Chat header */}
      <div className="bg-gray-100 p-3 border-b">
        <h3 className="font-medium">{taskTitle || 'Task Chat'}</h3>
      </div>

      {/* Messages */}
      <div className="p-3 overflow-y-auto" style={{ height: 'calc(100% - 110px)' }}>
        {messages.map((message) => (
          <div
            key={message.id}
            className={`mb-3 ${message.user?.id === user.id ? 'text-right' : 'text-left'} ${message.type === 'system' ? 'text-center' : ''}`}
          >
            {message.type === 'system' ? (
              <div className="inline-block py-1 px-3 bg-blue-50 text-blue-500 rounded-lg text-sm italic">
                {message.text}
              </div>
            ) : (
              <div>
                <div className="text-xs text-gray-500 mb-1">
                  {message.user?.name || message.user?.id || 'Unknown user'}
                </div>
                <div
                  className={`inline-block py-2 px-3 rounded-lg ${
                    message.user?.id === user.id ? 'bg-blue-500 text-white' : 'bg-gray-200'
                  }`}
                >
                  {message.text}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Message input */}
      <div className="p-3 border-t">
        <form onSubmit={handleSendMessage} className="flex">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type a message..."
            className="flex-1 p-2 border rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="submit"
            className="bg-blue-500 text-white px-4 py-2 rounded-r-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Send
          </button>
        </form>
      </div>
    </div>
  );
};

export default StreamUIChatBasic;

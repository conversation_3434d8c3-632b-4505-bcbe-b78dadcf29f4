import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle, ArrowLeft } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

const ResetPassword: React.FC = () => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validToken, setValidToken] = useState(true);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Check if we have a valid reset token in the URL
  useEffect(() => {
    const checkResetToken = async () => {
      try {
        if (process.env.NODE_ENV === 'development') {
          console.log('Checking for reset token...');
          }
        if (process.env.NODE_ENV === 'development') {
          console.log('URL:', window.location.href);

          }
        // First, check if we're already in a recovery session
        const { data: { session } } = await supabase.auth.getSession();

        if (session) {
          if (process.env.NODE_ENV === 'development') {
            console.log('Active session found');
            }
          setValidToken(true);
          return;
        }

        // Check for tokens in the URL hash (fragment)
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const accessToken = hashParams.get('access_token');
        const refreshToken = hashParams.get('refresh_token');
        const type = hashParams.get('type');

        if (process.env.NODE_ENV === 'development') {
    console.log('Hash params:', { accessToken: !!accessToken, refreshToken: !!refreshToken, type });
  }
        // If tokens are in the hash, use them
        if (accessToken && refreshToken && type === 'recovery') {
          if (process.env.NODE_ENV === 'development') {
            console.log('Found tokens in URL hash');

            }
          // Set the session from the recovery tokens
          const { error } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          });

          if (error) {
            console.error('Error setting session from hash tokens:', error);
            throw error;
          }

          setValidToken(true);
          return;
        }

        // Check for tokens in query parameters
        const queryParams = new URLSearchParams(window.location.search);
        const token = queryParams.get('token');
        const queryType = queryParams.get('type');

        if (process.env.NODE_ENV === 'development') {
    console.log('Query params:', { token: !!token, type: queryType });
  }
        // If we have a token in the query parameters
        if (token) {
          if (process.env.NODE_ENV === 'development') {
            console.log('Found token in query parameters');

            }
          // For Supabase's email confirmation flow
          if (queryType === 'recovery' || queryType === 'passwordRecovery') {
            try {
              // Try to exchange the token for a session
              const { error } = await supabase.auth.verifyOtp({
                token_hash: token,
                type: 'recovery'
              });

              if (error) {
                console.error('Error verifying OTP:', error);
                throw error;
              }

              setValidToken(true);
              return;
            } catch (verifyError) {
              console.error('Error verifying recovery token:', verifyError);
              // Continue to check other methods
            }
          }
        }

        // If we reach here, we couldn't find valid tokens
        if (process.env.NODE_ENV === 'development') {
          console.log('No valid tokens found in URL');
          }
        setValidToken(false);
        setError('Invalid or expired password reset link. Please request a new one.');
      } catch (err: any) {
        console.error('Error validating reset token:', err);
        setValidToken(false);
        setError('Invalid or expired password reset link. Please request a new one.');
      }
    };

    checkResetToken();
  }, []);

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!password) {
      setError('Please enter a new password');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (password.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Update the user's password
      const { error } = await supabase.auth.updateUser({
        password: password
      });

      if (error) {
        throw error;
      }

      // Show success message
      setSuccess(true);
      toast({
        title: 'Password updated',
        description: 'Your password has been successfully reset',
      });

      // Redirect to login after a short delay
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (err: any) {
      console.error('Error resetting password:', err);
      setError(err.message || 'Failed to reset password. Please try again.');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: err.message || 'Failed to reset password. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBackToForgotPassword = () => {
    navigate('/forgot-password');
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-bold tracking-tight text-gray-900">
            Reset your password
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Enter a new password for your account
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Create New Password</CardTitle>
            <CardDescription>
              Your password must be at least 8 characters long
            </CardDescription>
          </CardHeader>

          <CardContent>
            {!validToken ? (
              <Alert className="mb-4" variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Invalid Reset Link</AlertTitle>
                <AlertDescription>
                  This password reset link is invalid or has expired. Please request a new one.
                </AlertDescription>
              </Alert>
            ) : success ? (
              <Alert className="mb-4 bg-green-50 border-green-200">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertTitle className="text-green-800">Password Updated</AlertTitle>
                <AlertDescription className="text-green-700">
                  Your password has been successfully reset. You will be redirected to the login page.
                </AlertDescription>
              </Alert>
            ) : (
              <form onSubmit={handleResetPassword}>
                {error && (
                  <Alert className="mb-4" variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="password">New Password</Label>
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      minLength={8}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm New Password</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      required
                      minLength={8}
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={loading}
                  >
                    {loading ? 'Resetting...' : 'Reset Password'}
                  </Button>
                </div>
              </form>
            )}
          </CardContent>

          <CardFooter className="flex justify-center">
            {!validToken && (
              <Button variant="outline" onClick={handleBackToForgotPassword}>
                <ArrowLeft className="mr-1 h-4 w-4" />
                Request New Reset Link
              </Button>
            )}
            {validToken && !success && (
              <Button variant="link" onClick={() => navigate('/login')}>
                <ArrowLeft className="mr-1 h-4 w-4" />
                Back to login
              </Button>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default ResetPassword;

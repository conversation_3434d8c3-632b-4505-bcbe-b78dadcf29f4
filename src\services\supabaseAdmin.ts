// Supabase client with service role key for admin operations
import { createClient } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

// Use environment variables for sensitive credentials
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";

// For browser environments, we'll use the regular supabase client
// This ensures the code doesn't break when running in the browser
let supabaseAdmin = supabase;

// In a real production environment, you would use a server-side API
// to handle operations that require the service role key
if (process.env.NODE_ENV === 'development') {
  if (process.env.NODE_ENV === 'development') {
    console.log('Using regular Supabase client for admin operations in browser environment');
  }
}

export { supabaseAdmin };

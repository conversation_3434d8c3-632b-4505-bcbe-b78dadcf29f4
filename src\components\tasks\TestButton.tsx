import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface TestButtonProps {
  taskId: string;
  userId: string;
}

const TestButton = ({ taskId, userId }: TestButtonProps) => {
  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <Card className="mb-4 bg-purple-100 border-2 border-purple-500">
      <CardHeader>
        <CardTitle>Test Button (Development Only)</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <Button
            onClick={() => alert(`Task ID: ${taskId}, User ID: ${userId}`)}
            className="w-full bg-purple-600 hover:bg-purple-700"
          >
            Click Me
          </Button>
          <div className="p-2 bg-purple-200 rounded text-xs">
            <p><strong>Debug Info:</strong></p>
            <p>Task ID: {taskId}</p>
            <p>User ID: {userId}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TestButton;

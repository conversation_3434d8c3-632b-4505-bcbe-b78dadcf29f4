import Stripe from 'stripe';
import { supabase } from '@/integrations/supabase/client';

// Import standardized Stripe configuration
import stripeConfig, {
  STRIPE_PUBLIC_KEY,
  PLATFORM_FEE_PERCENTAGE,
  STRIPE_API_VERSION,
  STRIPE_ENABLED
} from '@/config/stripeConfig';

// Initialize Stripe only if the public key is available
let stripe: Stripe | null = null;

if (STRIPE_ENABLED && STRIPE_PUBLIC_KEY) {
  try {
    stripe = new Stripe(STRIPE_PUBLIC_KEY, {
      apiVersion: STRIPE_API_VERSION,
    });
  } catch (error) {
    console.error('Failed to initialize Stripe:', error);
  }
}

export interface StripeAccount {
  id: string;
  user_id: string;
  account_id: string;
  account_type: string;
  charges_enabled: boolean;
  payouts_enabled: boolean;
  account_status: string;
  refresh_token?: string;
  created_at: string;
  updated_at: string;
}

export interface Payment {
  id: string;
  task_id: string;
  offer_id: string;
  payer_id: string;
  payee_id: string;
  payment_intent_id?: string;
  transfer_id?: string;
  amount: number;
  platform_fee: number;
  supplier_amount: number;
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'refunded';
  currency: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface Invoice {
  id: string;
  payment_id: string;
  invoice_number: string;
  invoice_url?: string;
  stripe_invoice_id?: string;
  status: 'pending' | 'paid' | 'void';
  due_date?: string;
  paid_at?: string;
  created_at: string;
  updated_at: string;
}

// Helper function to check if Stripe is available
const checkStripeAvailable = (): boolean => {
  if (!STRIPE_ENABLED || !stripe) {
    console.warn('⚠️ Stripe is not available. Payment functionality is disabled.');
    return false;
  }
  return true;
};

export const stripeService = {
  /**
   * Create a Stripe Connect Express account for a user
   */
  async createExpressAccount(userId: string): Promise<StripeAccount | null> {
    try {
      if (process.env.NODE_ENV === 'development') {

        console.log(`Creating Express account for user ${userId}`);


        }
      // Check if Stripe is available
      if (!checkStripeAvailable()) {
        return null;
      }

      // Check if user already has an active account (excluding deleted accounts)
      const { data: existingAccount, error: fetchError } = await supabase
        .from('stripe_accounts')
        .select('*')
        .eq('user_id', userId)
        .neq('account_status', 'deleted')
        .maybeSingle();

      if (fetchError) {
        console.error('Error checking for existing account:', fetchError);
        return null;
      }

      if (existingAccount) {
        if (process.env.NODE_ENV === 'development') {

          console.log(`User ${userId} already has a Stripe account:`, existingAccount);

          }
        return existingAccount as StripeAccount;
      }

      // Get user profile for business information
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError) {
        console.error('Error fetching user profile:', profileError);
        return null;
      }

      // Create the Express account
      if (process.env.NODE_ENV === 'development') {
    console.log('About to call stripe.accounts.create...');
  }
      if (process.env.NODE_ENV === 'development') {

        console.log('stripe object:', typeof stripe, stripe ? Object.keys(stripe) : 'null');


        }
      if (!stripe) {
        throw new Error('Stripe client is not initialized');
      }

      const account = await stripe.accounts.create({
        type: 'express',
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true },
        },
        business_type: 'individual', // Default to individual, can be updated later
        business_profile: {
          mcc: '5734', // Computer Software Stores
          url: 'https://your-site.com', // Replace with your actual site URL
        },
        metadata: {
          user_id: userId,
        },
      });

      if (process.env.NODE_ENV === 'development') {


        console.log(`Created Stripe account ${account.id} for user ${userId}`);



        }
      // Store the account in our database
      const { data, error } = await supabase
        .from('stripe_accounts')
        .insert([{
          user_id: userId,
          account_id: account.id,
          account_type: 'express',
          charges_enabled: account.charges_enabled,
          payouts_enabled: account.payouts_enabled,
          account_status: 'pending',
        }])
        .select();

      if (error) {
        console.error('Error storing Stripe account:', error);
        return null;
      }

      // Update the user's profile with the Stripe account ID
      await supabase
        .from('profiles')
        .update({ stripe_account_id: account.id })
        .eq('id', userId);

      return data[0] as StripeAccount;
    } catch (error) {
      console.error('Error creating Express account:', error);
      // Log more detailed error information
      if (error instanceof Error) {
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      }
      // Log Stripe configuration
      if (process.env.NODE_ENV === 'development') {
    console.log('Stripe configuration:', {
        publicKey: STRIPE_PUBLIC_KEY ? 'Set' : 'Not set',
        apiVersion: STRIPE_API_VERSION
      });
  }
      return null;
    }
  },

  /**
   * Generate an onboarding link for a Stripe Connect Express account
   */
  async generateOnboardingLink(accountId: string): Promise<string | null> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log(`Generating onboarding link for account ${accountId}`);
  }
      // Check if Stripe is available
      if (!checkStripeAvailable()) {
        return null;
      }

      // Get the account from our database
      const { data: account, error } = await supabase
        .from('stripe_accounts')
        .select('*')
        .eq('account_id', accountId)
        .single();

      if (error) {
        console.error('Error fetching account:', error);
        return null;
      }

      // Generate the account link
      if (!stripe) {
        throw new Error('Stripe client is not initialized');
      }

      const accountLink = await stripe.accountLinks.create({
        account: accountId,
        refresh_url: import.meta.env.VITE_STRIPE_CONNECT_EXPRESS_REFRESH_URL ||
                     `${window.location.origin}/connect/express/refresh`,
        return_url: import.meta.env.VITE_STRIPE_CONNECT_EXPRESS_RETURN_URL ||
                    `${window.location.origin}/connect/express/return`,
        type: 'account_onboarding',
      });

      return accountLink.url;
    } catch (error) {
      console.error('Error generating onboarding link:', error);
      return null;
    }
  },

  /**
   * Generate a login link for the Stripe Connect Express dashboard
   */
  async generateDashboardLink(accountId: string): Promise<string | null> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log(`Generating dashboard link for account ${accountId}`);
  }
      // Call the server API to generate a dashboard link
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
      if (process.env.NODE_ENV === 'development') {
    console.log('Using API URL:', apiUrl);
  }
      const response = await fetch(`${apiUrl}/api/stripe-connect/dashboard-link`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ accountId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error generating dashboard link from API:', errorData);
        return null;
      }

      const data = await response.json();
      return data.url;
    } catch (error) {
      console.error('Error generating dashboard link:', error);
      return null;
    }
  },

  /**
   * Get the status of a Stripe Connect Express account
   */
  async getAccountStatus(accountId: string): Promise<any | null> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log(`Getting status for account ${accountId}`);
  }
      // Call the server API to get the account status
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
      const response = await fetch(`${apiUrl}/api/stripe-connect/account-status/${accountId}`);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error getting account status from API:', errorData);
        return null;
      }

      const status = await response.json();

      // Update our database with the latest status
      await supabase
        .from('stripe_accounts')
        .update({
          charges_enabled: status.charges_enabled,
          payouts_enabled: status.payouts_enabled,
          account_status: status.charges_enabled && status.payouts_enabled ? 'active' : 'pending',
          updated_at: new Date().toISOString(),
        })
        .eq('account_id', accountId);

      return status;
    } catch (error) {
      console.error('Error getting account status:', error);
      return null;
    }
  },

  /**
   * Create a payment with direct transfer to a connected account
   */
  async createPaymentWithDirectTransfer(
    taskId: string,
    offerId: string,
    amount: number
  ): Promise<Payment | null> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log(`Creating payment for task ${taskId}, offer ${offerId}, amount ${amount}`);
  }
      // Get the task and offer details
      const { data: task, error: taskError } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (taskError) {
        console.error('Error fetching task:', taskError);
        return null;
      }

      const { data: offer, error: offerError } = await supabase
        .from('offers')
        .select('*')
        .eq('id', offerId)
        .single();

      if (offerError) {
        console.error('Error fetching offer:', offerError);
        return null;
      }

      // For direct payments, we don't need the supplier's Stripe account
      if (process.env.NODE_ENV === 'development') {

        console.log(`Using direct payment for supplier ${offer.user_id}...`);


        }
      // Skip Stripe account check for direct payments

      // Calculate the platform fee and supplier amount
      const amountInCents = Math.round(amount * 100); // Convert to cents
      const platformFeeInCents = Math.round(amountInCents * (PLATFORM_FEE_PERCENTAGE / 100));
      const supplierAmountInCents = amountInCents - platformFeeInCents;

      // Create a payment record in our database
      const { data: payment, error: paymentError } = await supabase
        .from('payments')
        .insert([{
          task_id: taskId,
          offer_id: offerId,
          payer_id: task.user_id,
          payee_id: offer.user_id,
          amount: amount,
          platform_fee: platformFeeInCents / 100, // Convert back to dollars
          supplier_amount: supplierAmountInCents / 100, // Convert back to dollars
          status: 'pending',
          currency: 'gbp', // Default to GBP
        }])
        .select();

      if (paymentError) {
        console.error('Error creating payment record:', paymentError);
        return null;
      }

      return payment[0] as Payment;
    } catch (error) {
      console.error('Error creating payment with direct transfer:', error);
      return null;
    }
  },

  /**
   * Create a payment intent for a payment
   */
  async createPaymentIntent(paymentId: string): Promise<string | null> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log(`Creating payment intent for payment ${paymentId}`);
  }
      // Call the server API to create a payment intent
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
      if (process.env.NODE_ENV === 'development') {
    console.log('Using API URL:', apiUrl);
  }
      const response = await fetch(`${apiUrl}/api/stripe-connect/create-payment-intent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ paymentId }),
      });

      if (!response.ok) {
        let errorMessage = 'Server returned an error';
        try {
          const errorData = await response.json();
          console.error('Error creating payment intent from API:', errorData);
          errorMessage = errorData.error || errorMessage;
        } catch (parseError) {
          console.error('Error parsing error response:', parseError);
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      if (process.env.NODE_ENV === 'development') {
    console.log('Payment intent created successfully: completed');
  }
      return data.clientSecret;
    } catch (error) {
      console.error('Error creating payment intent:', error);
      return null;
    }
  },

  /**
   * Handle a webhook event from Stripe
   */
  async handleWebhookEvent(event: any): Promise<boolean> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log(`Handling webhook event: ${event.type}`);
  }
      switch (event.type) {
        case 'account.updated': {
          const account = event.data.object;

          // Update our database with the latest account status
          await supabase
            .from('stripe_accounts')
            .update({
              charges_enabled: account.charges_enabled,
              payouts_enabled: account.payouts_enabled,
              account_status: account.charges_enabled && account.payouts_enabled ? 'active' : 'pending',
              updated_at: new Date().toISOString(),
            })
            .eq('account_id', account.id);

          break;
        }

        case 'payment_intent.succeeded': {
          const paymentIntent = event.data.object;

          // Update the payment record
          await supabase
            .from('payments')
            .update({
              status: 'succeeded',
              updated_at: new Date().toISOString(),
            })
            .eq('payment_intent_id', paymentIntent.id);

          // Update the task status
          if (paymentIntent.metadata?.task_id) {
            await supabase
              .from('tasks')
              .update({
                payment_status: 'paid',
                status: 'completed',
              })
              .eq('id', paymentIntent.metadata.task_id);
          }

          break;
        }

        case 'payment_intent.payment_failed': {
          const paymentIntent = event.data.object;

          // Update the payment record
          await supabase
            .from('payments')
            .update({
              status: 'failed',
              updated_at: new Date().toISOString(),
            })
            .eq('payment_intent_id', paymentIntent.id);

          break;
        }
      }

      return true;
    } catch (error) {
      console.error('Error handling webhook event:', error);
      return false;
    }
  },

  /**
   * Delete a Stripe Connect Express account
   */
  async deleteAccount(accountId: string): Promise<boolean> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log(`Deleting Stripe account ${accountId}`);
  }
      // Call the server API to delete the account
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
      if (process.env.NODE_ENV === 'development') {
    console.log('Using API URL for delete:', apiUrl);
  }
      const deleteUrl = `${apiUrl}/api/stripe-connect/delete-account/${accountId}`;
      if (process.env.NODE_ENV === 'development') {
    console.log('Full delete URL:', deleteUrl);
  }
      const response = await fetch(deleteUrl, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error deleting Stripe account:', errorData);
        return false;
      }

      const result = await response.json();

      if (result.deleted) {
        if (process.env.NODE_ENV === 'development') {
    console.log(`Successfully deleted Stripe account ${accountId}`);
  }
        // Update local state if needed
        // For example, you might want to refresh the user's profile

        return true;
      } else {
        console.error('Failed to delete Stripe account:', result);
        return false;
      }
    } catch (error) {
      console.error('Error deleting Stripe account:', error);
      return false;
    }
  },

  /**
   * Create an invoice for a payment
   */
  async createInvoice(paymentId: string): Promise<Invoice | null> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log(`Creating invoice for payment ${paymentId}`);
  }
      // Call the server API to create an invoice
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
      const response = await fetch(`${apiUrl}/api/stripe-connect/create-invoice`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ paymentId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error creating invoice from API:', errorData);
        return null;
      }

      const data = await response.json();

      // Get the payment details to get the task_id
      const { data: payment, error: paymentError } = await supabase
        .from('payments')
        .select('task_id')
        .eq('id', paymentId)
        .single();

      if (paymentError) {
        console.error('Error getting payment details:', paymentError);
        return null;
      }

      // Update our database with the invoice information
      const { data: invoice, error } = await supabase
        .from('invoices')
        .insert([
          {
            payment_id: paymentId,
            task_id: payment.task_id, // Include the task_id
            invoice_number: data.number,
            stripe_invoice_id: data.id,
            invoice_url: data.hosted_invoice_url,
            status: data.status,
            due_date: data.due_date ? new Date(data.due_date * 1000).toISOString() : null,
          },
        ])
        .select();

      if (error) {
        console.error('Error storing invoice in database:', error);
        return null;
      }

      return invoice[0] as Invoice;
    } catch (error) {
      console.error('Error creating invoice:', error);
      return null;
    }
  },

  /**
   * Get an invoice by ID
   */
  async getInvoice(invoiceId: string): Promise<any | null> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log(`Getting invoice ${invoiceId}`);
  }
      // Call the server API to get the invoice
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
      const response = await fetch(`${apiUrl}/api/stripe-connect/invoice/${invoiceId}`);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error getting invoice from API:', errorData);
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting invoice:', error);
      return null;
    }
  },

  /**
   * Get the PDF URL for an invoice
   */
  async getInvoicePdfUrl(invoiceId: string): Promise<string | null> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log(`Getting PDF URL for invoice ${invoiceId}`);
  }
      // Call the server API to get the invoice PDF URL
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
      const response = await fetch(`${apiUrl}/api/stripe-invoice/invoice/${invoiceId}/pdf`);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error getting invoice PDF URL from API:', errorData);
        return null;
      }

      const data = await response.json();
      return data.url;
    } catch (error) {
      console.error('Error getting invoice PDF URL:', error);
      return null;
    }
  },

  /**
   * List invoices for an organization
   */
  async listOrganizationInvoices(organizationId: string): Promise<any[] | null> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log(`Listing invoices for organization ${organizationId}`);
  }
      // Get all users in the organization
      const { data: orgUsers, error: orgUsersError } = await supabase
        .from('profiles')
        .select('id')
        .eq('organization_id', organizationId);

      if (orgUsersError) {
        console.error('Error fetching organization users:', orgUsersError);
        return null;
      }

      const userIds = orgUsers.map(user => user.id);

      // Get all payments made by these users
      const { data: payments, error: paymentsError } = await supabase
        .from('payments')
        .select(`
          id,
          task_id,
          offer_id,
          amount,
          status,
          created_at,
          tasks (title)
        `)
        .in('payer_id', userIds);

      if (paymentsError) {
        console.error('Error fetching payments:', paymentsError);
        return null;
      }

      // Get all invoices for these payments
      const { data: invoices, error: invoicesError } = await supabase
        .from('invoices')
        .select('*')
        .in(
          'payment_id',
          payments.map(p => p.id)
        );

      if (invoicesError) {
        console.error('Error fetching invoices:', invoicesError);
        return null;
      }

      // Combine payment and invoice data
      const combinedInvoices = invoices.map(invoice => {
        const relatedPayment = payments.find(p => p.id === invoice.payment_id);
        return {
          ...invoice,
          amount: relatedPayment?.amount || 0,
          task_title: relatedPayment?.tasks?.title,
          task_id: relatedPayment?.task_id,
        };
      });

      return combinedInvoices;
    } catch (error) {
      console.error('Error listing organization invoices:', error);
      return null;
    }
  },

  /**
   * Send an invoice email to the customer
   * @returns The response from the server, or false if there was an error
   */
  async sendInvoiceEmail(invoiceId: string): Promise<any> {
    try {
      if (process.env.NODE_ENV === 'development') {
    console.log(`Sending email for invoice ${invoiceId}`);
  }
      // Get the user's session
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('No active session');
      }

      // Call the Supabase Edge Function
      const supabaseFunctionsUrl = import.meta.env.VITE_SUPABASE_FUNCTIONS_URL ||
        'https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1';

      const response = await fetch(
        `${supabaseFunctionsUrl}/send-invoice-email`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`,
          },
          body: JSON.stringify({ invoiceId }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error sending invoice email:', errorData);
        return false;
      }

      const data = await response.json();

      // Return the full response data so we can use the message and mode
      return data;
    } catch (error) {
      console.error('Error sending invoice email:', error);
      return false;
    }
  },
};
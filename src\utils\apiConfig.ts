/**
 * API Configuration Utility
 *
 * This utility provides functions for getting the correct API base URL
 * based on the current environment (development or production).
 */

/**
 * Get the base URL for API requests
 * In development, this will be the localhost URL
 * In production, this will be an empty string (for relative URLs)
 */
export const getApiBaseUrl = (): string => {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    // Check if we're in PWA mode
    const isPWA = window.matchMedia('(display-mode: standalone)').matches;

    // In development, use the environment variable or default to localhost:3002
    if (process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost') {
      const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3002';
      return baseUrl;
    }

    // For PWA in production, use the current origin to ensure requests go to the right place
    if (isPWA) {
      const origin = window.location.origin;
      if (process.env.NODE_ENV === 'development') {

        console.log('[apiConfig] Using PWA API base URL (current origin):', origin);

        }
      return origin;
    }
  }

  // In production, use relative URLs (empty string)
  // This ensures that API requests go to the same domain as the app
  if (process.env.NODE_ENV === 'development') {

    console.log('[apiConfig] Using production API base URL (empty string for relative URLs)');

    }
  return '';
};

/**
 * Get the full URL for a GetStream API endpoint
 * @param endpoint The API endpoint path (e.g., '/token')
 */
export const getStreamApiUrl = (endpoint: string): string => {
  // Ensure endpoint starts with a slash
  const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

  // Get the base URL
  const baseUrl = getApiBaseUrl();

  // Determine if we're in development or production
  const isDevelopment = typeof window !== 'undefined' && (
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1' ||
    window.location.hostname.includes('192.168.') ||
    // Add additional checks for mobile devices
    window.location.href.includes('192.168.')
  );

  // Get the server IP address from the current URL
  const serverIP = typeof window !== 'undefined' && window.location.hostname.includes('192.168.')
    ? window.location.hostname
    : '*************'; // Fallback to your computer's IP

  // For PWA in production, try to use a more reliable URL construction
  const isPWA = typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches;
  const isProduction = process.env.NODE_ENV === 'production';

  // Environment validation for development only
  if (process.env.NODE_ENV === 'development') {
    if (process.env.NODE_ENV === 'development') {
    console.log('[getStreamApiUrl] Development mode - endpoint:', normalizedEndpoint);
  }
  }

  // Construct the URL
  let url;

  if (isDevelopment) {
    // In development, use the local API server with the correct IP address
    // Use the IP address instead of localhost for mobile devices
    const isMobile = typeof window !== 'undefined' && /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile || window.location.hostname.includes('192.168.')) {
      // For mobile devices, use the server's IP address
      url = `http://${serverIP}:3002/api/getstream${normalizedEndpoint}`;
      if (process.env.NODE_ENV === 'development') {
    console.log('[getStreamApiUrl] Using IP-based API server for mobile:', url);
  }
    } else {
      // For desktop development, use localhost
      url = `http://localhost:3002/api/getstream${normalizedEndpoint}`;
      if (process.env.NODE_ENV === 'development') {
    console.log('[getStreamApiUrl] Using localhost API server in development:', url);
  }
    }
  } else if (isPWA && isProduction) {
    // For PWA in production, use the full origin to avoid relative URL issues
    const origin = typeof window !== 'undefined' ? window.location.origin : '';
    url = `${origin}/api/getstream${normalizedEndpoint}`;
    if (process.env.NODE_ENV === 'development') {
    console.log('[getStreamApiUrl] Using full origin URL for PWA in production:', url);
  }
  } else {
    // Standard URL construction
    url = `${baseUrl}/api/getstream${normalizedEndpoint}`;
    if (process.env.NODE_ENV === 'development') {
    console.log('[getStreamApiUrl] Using standard URL construction:', url);
  }
  }

  return url;
};

export default {
  getApiBaseUrl,
  getStreamApiUrl
};

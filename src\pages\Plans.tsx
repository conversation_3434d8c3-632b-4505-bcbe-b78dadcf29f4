import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Check, X, HelpCircle, Building, Home, Landmark, Mail, Users, Shield, ClipboardList, BarChart } from 'lucide-react';
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const Plans = () => {
  return (
    <MainLayout>
      <div className="container mx-auto py-12 px-4">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="inline-block px-3 py-1 bg-classtasker-blue/10 text-classtasker-blue rounded-full text-sm font-medium mb-4">
            Flexible Pricing Options
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Choose the Right Plan for Your School</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            ClassTasker offers flexible pricing options to suit schools of all sizes, from individual schools to multi-academy trusts.
          </p>
          <div className="mt-6 inline-block bg-green-100 text-green-800 px-4 py-2 rounded-full font-medium">
            All plans include a 3-month free trial!
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid gap-8 md:grid-cols-3 mb-16">
          {/* Individual School Plan */}
          <Card className="border-2 border-gray-200 hover:border-classtasker-blue transition-colors">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Building className="h-8 w-8 text-classtasker-blue" />
              </div>
              <CardTitle className="text-2xl">Individual School</CardTitle>
              <CardDescription className="text-lg mt-2">Perfect for single schools</CardDescription>
            </CardHeader>
            <CardContent className="text-center pb-4">
              <div className="mb-6">
                <span className="text-4xl font-bold">Free</span>
                <span className="text-gray-500 ml-2">up to 5 users</span>
              </div>
              <div className="border-t border-b border-gray-100 py-6 px-6">
                <ul className="space-y-4 text-left">
                  <FeatureItem included text="Internal task management" />
                  <FeatureItem included text="External contractor hiring" />
                  <FeatureItem included text="Basic compliance tracking" />
                  <FeatureItem included text="Up to 5 staff accounts" />
                  <FeatureItem included text="Email notifications" />
                  <FeatureItem included text="Standard support" />
                  <FeatureItem included={false} text="Multi-school dashboard" />
                  <FeatureItem included={false} text="Advanced compliance reporting" />
                  <FeatureItem included={false} text="Custom domain management" />
                </ul>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4 pt-0">
              <Button asChild className="w-full bg-classtasker-blue hover:bg-blue-600">
                <Link to="/register">Join Now for £0</Link>
              </Button>
              <p className="text-sm text-gray-500 text-center">No credit card required</p>
            </CardFooter>
          </Card>

          {/* Small Group Plan */}
          <Card className="border-2 border-classtasker-green relative">
            <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-classtasker-green text-white px-4 py-1 rounded-full text-sm font-medium">
              Most Popular
            </div>
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Home className="h-8 w-8 text-classtasker-green" />
              </div>
              <CardTitle className="text-2xl">Small Group</CardTitle>
              <CardDescription className="text-lg mt-2">For up to 3 schools</CardDescription>
            </CardHeader>
            <CardContent className="text-center pb-4">
              <div className="mb-6">
                <span className="text-4xl font-bold">£24.99</span>
                <span className="text-gray-500 ml-2">per user/year</span>
              </div>
              <div className="border-t border-b border-gray-100 py-6 px-6">
                <ul className="space-y-4 text-left">
                  <FeatureItem included text="Everything in Individual plan" />
                  <FeatureItem included text="Up to 3 schools" />
                  <FeatureItem included text="Cross-school task management" />
                  <FeatureItem included text="Up to 20 staff accounts" />
                  <FeatureItem included text="Advanced compliance tracking" />
                  <FeatureItem included text="Priority support" />
                  <FeatureItem included text="Basic analytics dashboard" />
                  <FeatureItem included={false} text="Custom domain management" />
                  <FeatureItem included={false} text="API access" />
                </ul>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4 pt-0">
              <Button asChild className="w-full bg-classtasker-green hover:bg-green-600">
                <Link to="/register">Start Free Trial</Link>
              </Button>
              <p className="text-sm text-gray-500 text-center">3 months free, then £24.99 per user/year</p>
            </CardFooter>
          </Card>

          {/* Multi-Academy Trust Plan */}
          <Card className="border-2 border-gray-200 hover:border-classtasker-purple transition-colors">
            <CardHeader className="text-center pb-4">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Landmark className="h-8 w-8 text-classtasker-purple" />
              </div>
              <CardTitle className="text-2xl">Multi-Academy Trust</CardTitle>
              <CardDescription className="text-lg mt-2">For larger organizations</CardDescription>
            </CardHeader>
            <CardContent className="text-center pb-4">
              <div className="mb-6">
                <span className="text-4xl font-bold">Custom</span>
                <span className="text-gray-500 ml-2">contact for pricing</span>
              </div>
              <div className="border-t border-b border-gray-100 py-6 px-6">
                <ul className="space-y-4 text-left">
                  <FeatureItem included text="Everything in Small Group plan" />
                  <FeatureItem included text="Unlimited schools" />
                  <FeatureItem included text="Custom domain management" />
                  <FeatureItem included text="Email domain management" />
                  <FeatureItem included text="Advanced analytics & reporting" />
                  <FeatureItem included text="Dedicated account manager" />
                  <FeatureItem included text="API access" />
                  <FeatureItem included text="Custom integrations" />
                  <FeatureItem included text="SLA guarantees" />
                </ul>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4 pt-0">
              <Button asChild className="w-full bg-classtasker-purple hover:bg-purple-600">
                <Link to="/contact">Contact Sales</Link>
              </Button>
              <p className="text-sm text-gray-500 text-center">Custom pricing based on your needs</p>
            </CardFooter>
          </Card>
        </div>

        {/* Feature Comparison */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Feature Comparison</h2>

          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b-2 border-gray-200">
                  <th className="text-left py-4 px-4 font-semibold text-gray-600">Feature</th>
                  <th className="text-center py-4 px-4 font-semibold text-classtasker-blue">Individual School</th>
                  <th className="text-center py-4 px-4 font-semibold text-classtasker-green">Small Group</th>
                  <th className="text-center py-4 px-4 font-semibold text-classtasker-purple">Multi-Academy Trust</th>
                </tr>
              </thead>
              <tbody>
                <FeatureRow
                  feature="Schools"
                  individual="1 school"
                  smallGroup="Up to 3 schools"
                  mat="Unlimited"
                  icon={<Building className="h-4 w-4" />}
                />
                <FeatureRow
                  feature="Staff accounts"
                  individual="Up to 5"
                  smallGroup="Up to 20"
                  mat="Unlimited"
                  icon={<Users className="h-4 w-4" />}
                />
                <FeatureRow
                  feature="Compliance tracking"
                  individual="Basic"
                  smallGroup="Advanced"
                  mat="Enterprise-grade"
                  icon={<Shield className="h-4 w-4" />}
                />
                <FeatureRow
                  feature="Task management"
                  individual="School-level"
                  smallGroup="Cross-school"
                  mat="Trust-wide"
                  icon={<ClipboardList className="h-4 w-4" />}
                />
                <FeatureRow
                  feature="Analytics & reporting"
                  individual="Basic"
                  smallGroup="Advanced"
                  mat="Custom dashboards"
                  icon={<BarChart className="h-4 w-4" />}
                />
                <FeatureRow
                  feature="Domain management"
                  individual="No"
                  smallGroup="No"
                  mat="Yes"
                  icon={<Mail className="h-4 w-4" />}
                />
                <FeatureRow
                  feature="Support"
                  individual="Standard"
                  smallGroup="Priority"
                  mat="Dedicated manager"
                  icon={<HelpCircle className="h-4 w-4" />}
                />
              </tbody>
            </table>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8">Frequently Asked Questions</h2>

          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>How does the 3-month free trial work?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>All plans include a 3-month free trial with full access to all features included in your selected plan. No credit card is required to start your trial. We'll send you a reminder before your trial ends, and you can choose to continue or cancel at any time.</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>How is the per-user pricing calculated?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>For the Small Group plan, pricing is based on the total number of users across all schools in your group. For example, if you have 3 schools with 5 users each (15 total), your annual cost would be £374.85 per year (15 users × £24.99).</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Can I upgrade my plan later?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Yes, you can upgrade your plan at any time. If you start with the Individual School plan and later want to add more schools, you can easily upgrade to the Small Group or Multi-Academy Trust plan. Your existing data will be preserved.</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>What is domain management?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>Domain management allows Multi-Academy Trust customers to use their own email domains for authentication and user management. This means staff can log in with their existing school email addresses, and you can automatically assign roles based on email domains.</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>How do payments work for external contractors?</CardTitle>
              </CardHeader>
              <CardContent>
                <p>All payments are made to ClassTasker using your school's preferred payment methods, including BACS, within 15 days upon invoice. We support batch payment processing and manage all supplier payments on your behalf, ensuring contractors are paid promptly once work is completed satisfactorily. This streamlined approach simplifies your financial processes while protecting both schools and contractors.</p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-br from-classtasker-blue/10 to-classtasker-purple/10 p-8 rounded-xl">
          <h2 className="text-2xl font-bold mb-4">Ready to get started?</h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Join ClassTasker today and see how we can transform your school maintenance and compliance management.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild size="lg" className="bg-classtasker-blue hover:bg-blue-600">
              <Link to="/register">Join Now</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link to="/contact">Contact Sales</Link>
            </Button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

// Helper components
const FeatureItem = ({ included, text }: { included: boolean; text: string }) => (
  <li className="flex items-start">
    {included ? (
      <Check className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
    ) : (
      <X className="h-5 w-5 text-gray-300 mr-3 mt-0.5 flex-shrink-0" />
    )}
    <span className={included ? "text-gray-800" : "text-gray-400"}>{text}</span>
  </li>
);

const FeatureRow = ({
  feature,
  individual,
  smallGroup,
  mat,
  icon
}: {
  feature: string;
  individual: string;
  smallGroup: string;
  mat: string;
  icon: React.ReactNode;
}) => (
  <tr className="border-b border-gray-100">
    <td className="py-4 px-4 flex items-center">
      <span className="text-gray-500 mr-2">{icon}</span>
      <span>{feature}</span>
    </td>
    <td className="text-center py-4 px-4">{individual}</td>
    <td className="text-center py-4 px-4">{smallGroup}</td>
    <td className="text-center py-4 px-4">{mat}</td>
  </tr>
);

export default Plans;

/**
 * EnhancedTaskWrapper
 * 
 * This component serves as a wrapper for the EnhancedTask page.
 * It can be used to replace the existing TaskWrapper component.
 */

import React from 'react';
import EnhancedTask from './EnhancedTask';

const EnhancedTaskWrapper: React.FC = () => {
  if (process.env.NODE_ENV === 'development') {
    console.log('EnhancedTaskWrapper: Using EnhancedTask page');
    }
  return <EnhancedTask />;
};

export default EnhancedTaskWrapper;

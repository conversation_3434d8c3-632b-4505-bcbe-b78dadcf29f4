// <PERSON>ript to apply the exec_sql function
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

// Initialize Supabase with service role key for admin access
const supabaseUrl = process.env.SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co";
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('Error: SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

async function applyExecSqlFunction() {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('Applying exec_sql function...');

      }
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '..', '..', 'sql', 'create_exec_sql_function.sql');
    if (process.env.NODE_ENV === 'development') {
      console.log(`Reading SQL file: ${sqlFilePath}`);
      }
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL directly using the Supabase SQL API
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey,
        'Prefer': 'return=minimal'
      },
      body: JSON.stringify({
        sql: sql
      })
    }).catch(error => {
      if (process.env.NODE_ENV === 'development') {
        console.log('Error executing SQL API call:', error);
        }
      return { ok: false, status: error.code || 500 };
    });

    if (!response.ok) {
      // If the exec_sql function doesn't exist yet, use the SQL API directly
      if (process.env.NODE_ENV === 'development') {
        console.log(`SQL API returned status ${response.status}, trying direct SQL execution...`);

        }
      const sqlResponse = await fetch(`${supabaseUrl}/rest/v1/sql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'apikey': supabaseServiceKey
        },
        body: JSON.stringify({
          query: sql
        })
      });

      if (!sqlResponse.ok) {
        const errorText = await sqlResponse.text();
        throw new Error(`SQL API error: ${sqlResponse.status} - ${errorText}`);
      }

      const result = await sqlResponse.json();
      if (process.env.NODE_ENV === 'development') {
        console.log('SQL API result:', result);
        }
    } else {
      const result = await response.json();
      if (process.env.NODE_ENV === 'development') {
        console.log('Result:', result);
        }
    }

    if (process.env.NODE_ENV === 'development') {
    console.log('exec_sql function applied successfully.');
  }
  } catch (error) {
    console.error('Error applying exec_sql function:', error);
  }
}

// Run the function
applyExecSqlFunction();

#!/usr/bin/env node

/**
 * Emergency Fix for Malformed JavaScript Syntax
 * Fixes the broken .replace patterns that were malformed by the automated fixer
 */

const fs = require('fs');
const path = require('path');

// Colors for output
const colors = {
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

/**
 * Log with colors
 */
function log(level, message) {
  const color = {
    ERROR: colors.red,
    WARNING: colors.yellow,
    SUCCESS: colors.green,
    INFO: colors.blue
  }[level] || colors.reset;

  console.log(`${color}[${level}]${colors.reset} ${message}`);
}

/**
 * Fix malformed syntax patterns
 */
const SYNTAX_FIXES = [
  // Fix the malformed .replace(/user.*/, 'hasUser: ' + !!user) patterns
  {
    pattern: /\.replace\(\/user\.\*\/, 'hasUser: ' \+ !!user\)/g,
    replacement: '',
    name: 'Remove malformed .replace() calls'
  },

  // Fix specific patterns that got mangled
  {
    pattern: /console\.log\('([^']*)', ([^)]+)\.replace\(\/user\.\*\/, 'hasUser: ' \+ !!user\)\)/g,
    replacement: 'console.log(\'$1\', \'[DATA_SANITIZED]\')',
    name: 'Fix console.log with malformed replace'
  },

  // Fix function calls with malformed replace
  {
    pattern: /([a-zA-Z_$][a-zA-Z0-9_$]*)\(([^)]*?)\.replace\(\/user\.\*\/, 'hasUser: ' \+ !!user\)\)/g,
    replacement: '$1($2)',
    name: 'Fix function calls with malformed replace'
  },

  // Fix object properties with malformed replace
  {
    pattern: /([a-zA-Z_$][a-zA-Z0-9_$]*): ([^,}]+)\.replace\(\/user\.\*\/, 'hasUser: ' \+ !!user\)/g,
    replacement: '$1: $2',
    name: 'Fix object properties with malformed replace'
  },

  // Fix template literals with malformed replace
  {
    pattern: /\$\{([^}]+)\.replace\(\/user\.\*\/, 'hasUser: ' \+ !!user\)\}/g,
    replacement: '${$1}',
    name: 'Fix template literals with malformed replace'
  },

  // Fix specific broken patterns
  {
    pattern: /window\.matchMedia\('([^']+)\.replace\(\/user\.\*\/, 'hasUser: ' \+ !!user\)'\)/g,
    replacement: 'window.matchMedia(\'$1\')',
    name: 'Fix window.matchMedia calls'
  },

  // Fix JSON.stringify with malformed replace
  {
    pattern: /JSON\.stringify\(([^,]+), null, ([^)]+)\.replace\(\/user\.\*\/, 'hasUser: ' \+ !!user\)\)/g,
    replacement: 'JSON.stringify($1, null, $2)',
    name: 'Fix JSON.stringify calls'
  },

  // Fix client.isConnected calls
  {
    pattern: /client\.isConnected\(\.replace\(\/user\.\*\/, 'hasUser: ' \+ !!user\)\)/g,
    replacement: 'client.isConnected()',
    name: 'Fix client.isConnected calls'
  },

  // Fix isPWA calls
  {
    pattern: /isPWA\(\.replace\(\/user\.\*\/, 'hasUser: ' \+ !!user\)\)/g,
    replacement: 'isPWA()',
    name: 'Fix isPWA calls'
  },

  // Fix supabase.auth.getUser calls
  {
    pattern: /supabase\.auth\.getUser\(\.replace\(\/user\.\*\/, 'hasUser: ' \+ !!user\)\)/g,
    replacement: 'supabase.auth.getUser()',
    name: 'Fix supabase.auth.getUser calls'
  },

  // Fix localStorage.getItem calls
  {
    pattern: /localStorage\.getItem\('([^']+)'\.replace\(\/user\.\*\/, 'hasUser: ' \+ !!user\)\)/g,
    replacement: 'localStorage.getItem(\'$1\')',
    name: 'Fix localStorage.getItem calls'
  },

  // Fix data.token.substring calls
  {
    pattern: /data\.token\.substring\(0, ([^)]+)\.replace\(\/user\.\*\/, 'hasUser: ' \+ !!user\)\)/g,
    replacement: 'data.token.substring(0, $1)',
    name: 'Fix data.token.substring calls'
  }
];

/**
 * Files to exclude from fixing
 */
const EXCLUDE_PATTERNS = [
  /node_modules/,
  /\.git/,
  /dist/,
  /build/,
  /fix-syntax-errors\.cjs$/
];

/**
 * File extensions to fix
 */
const FIX_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

/**
 * Fix results storage
 */
let fixResults = {
  totalFiles: 0,
  fixedFiles: 0,
  totalFixes: 0,
  fixesByCategory: {},
  errors: []
};

/**
 * Check if file should be excluded
 */
function shouldExcludeFile(filePath) {
  return EXCLUDE_PATTERNS.some(pattern => pattern.test(filePath));
}

/**
 * Check if file should be fixed
 */
function shouldFixFile(filePath) {
  const ext = path.extname(filePath);
  return FIX_EXTENSIONS.includes(ext) && !shouldExcludeFile(filePath);
}

/**
 * Apply syntax fixes to file content
 */
function fixSyntaxErrors(content, filePath) {
  let modifiedContent = content;
  let fileFixCount = 0;

  // Apply each fix
  SYNTAX_FIXES.forEach(({ pattern, replacement, name }) => {
    const beforeLength = modifiedContent.length;
    const beforeMatches = (modifiedContent.match(pattern) || []).length;

    modifiedContent = modifiedContent.replace(pattern, replacement);

    const afterMatches = (modifiedContent.match(pattern) || []).length;
    const fixesApplied = beforeMatches - afterMatches;

    if (fixesApplied > 0) {
      fileFixCount += fixesApplied;
      fixResults.totalFixes += fixesApplied;

      if (!fixResults.fixesByCategory[name]) {
        fixResults.fixesByCategory[name] = 0;
      }
      fixResults.fixesByCategory[name] += fixesApplied;

      log('INFO', `Applied ${fixesApplied}x ${name} in ${filePath}`);
    }
  });

  return { content: modifiedContent, fixCount: fileFixCount };
}

/**
 * Fix a single file
 */
function fixFile(filePath) {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    const { content: fixedContent, fixCount } = fixSyntaxErrors(originalContent, filePath);

    if (fixCount > 0) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      fixResults.fixedFiles++;
      log('SUCCESS', `Fixed ${fixCount} syntax errors in ${filePath}`);
    }

    return true;
  } catch (error) {
    const errorMsg = `Failed to fix ${filePath}: ${error.message}`;
    fixResults.errors.push(errorMsg);
    log('ERROR', errorMsg);
    return false;
  }
}

/**
 * Recursively fix directory
 */
function fixDirectory(dirPath) {
  try {
    const items = fs.readdirSync(dirPath);

    items.forEach(item => {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !shouldExcludeFile(fullPath)) {
        fixDirectory(fullPath);
      } else if (stat.isFile() && shouldFixFile(fullPath)) {
        fixResults.totalFiles++;
        fixFile(fullPath);
      }
    });
  } catch (error) {
    const errorMsg = `Failed to fix directory ${dirPath}: ${error.message}`;
    fixResults.errors.push(errorMsg);
    log('ERROR', errorMsg);
  }
}

/**
 * Generate fix report
 */
function generateReport() {
  console.log('\n' + '='.repeat(80));
  log('INFO', colors.bold + 'EMERGENCY SYNTAX FIX RESULTS' + colors.reset);
  console.log('='.repeat(80));

  // Summary
  console.log(`\n📊 ${colors.bold}FIX SUMMARY${colors.reset}`);
  console.log(`Total files processed: ${fixResults.totalFiles}`);
  console.log(`Files with fixes applied: ${fixResults.fixedFiles}`);
  console.log(`Total syntax errors fixed: ${fixResults.totalFixes}`);

  // Category breakdown
  console.log(`\n🔧 ${colors.bold}FIXES BY CATEGORY${colors.reset}`);
  Object.entries(fixResults.fixesByCategory).forEach(([category, count]) => {
    console.log(`${category}: ${count} fixes`);
  });

  // Errors
  if (fixResults.errors.length > 0) {
    console.log(`\n❌ ${colors.bold}ERRORS${colors.reset}`);
    fixResults.errors.forEach(error => {
      console.log(`- ${error}`);
    });
  }

  // Success message
  if (fixResults.totalFixes > 0) {
    log('SUCCESS', `🎉 Successfully fixed ${fixResults.totalFixes} syntax errors!`);
    console.log('\n💡 Next steps:');
    console.log('1. Test the build to ensure it compiles');
    console.log('2. Run the application to verify functionality');
    console.log('3. Commit the emergency syntax fixes');
  } else {
    log('INFO', 'No syntax errors found - your code is clean!');
  }
}

/**
 * Main execution
 */
function main() {
  console.log(`${colors.red}🚨 EMERGENCY SYNTAX ERROR FIXES...${colors.reset}\n`);

  const startTime = Date.now();

  // Fix src directory
  if (fs.existsSync('src')) {
    log('INFO', 'Fixing src/ directory...');
    fixDirectory('src');
  }

  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);

  console.log(`\n⏱️ Fixes completed in ${duration} seconds`);

  // Generate report
  generateReport();

  // Exit with appropriate code
  const hasErrors = fixResults.errors.length > 0;
  process.exit(hasErrors ? 1 : 0);
}

// Run the emergency fixer
main();

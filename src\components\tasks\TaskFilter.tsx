
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Search, Filter, X, Tag, MapPin, PoundSterling } from "lucide-react";
import LocationSearch from "@/components/ui/location-search";
import { clearLocationCaches } from "@/utils/location-utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FILTER_CATEGORIES } from "@/constants/categories";

const TaskFilter = ({ onFilter }: { onFilter: (filters: any) => void }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [category, setCategory] = useState("All Categories");
  const [location, setLocation] = useState("");
  const [radius, setRadius] = useState(10);
  const [budget, setBudget] = useState([0, 5000]);

  // Ensure radius is always at least 5 miles
  const handleRadiusChange = (value: number) => {
    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: TaskFilter radius changed to: completed');
  }
    setRadius(Math.max(5, value));
  };

  // Handle location change
  const handleLocationChange = (newLocation: string) => {
    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Location changed to:', newLocation);
  }
    setLocation(newLocation);

    if (newLocation) {
      // Set appropriate radius based on location specificity
      // More specific locations (longer strings, contains commas) get smaller radius
      const isSpecific = newLocation.includes(',') || newLocation.length > 15;
      const appropriateRadius = isSpecific ? 10 : 20;

      // Only increase radius if it's too small
      if (radius < appropriateRadius) {
        if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Setting appropriate radius of ${appropriateRadius} miles for location: ${newLocation}`);
  }
        setRadius(appropriateRadius);
      }
    } else {
      // If location is cleared, reset radius to default
      setRadius(10);
    }
  };

  // We'll remove the useEffect hook to prevent clearing caches on every render
  // This will help prevent flickering

  const handleFilter = () => {
    // Ensure radius is reasonable when location is provided
    let effectiveRadius = radius;

    if (location) {
      // Set minimum radius based on location string length
      // Longer location strings are likely more specific and should have smaller minimum radius
      const minRadius = location.length > 15 ? 5 : 10;

      if (effectiveRadius < minRadius) {
        if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Enforcing minimum radius of ${minRadius} miles for location filtering`);
  }
        effectiveRadius = minRadius;
        setRadius(minRadius); // Update the UI as well
      }

      // Also enforce a maximum radius to ensure filtering works correctly
      const maxRadius = 45; // Maximum radius for effective filtering
      if (effectiveRadius > maxRadius) {
        if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Limiting maximum radius to ${maxRadius} miles for effective filtering`);
  }
        effectiveRadius = maxRadius;
        setRadius(maxRadius); // Update the UI as well
      }
    }

    if (process.env.NODE_ENV === 'development') {
    console.log(`DEBUG: Applying filter with location: "${location}" and radius: ${effectiveRadius} miles`);
  }
    // Clear caches only when applying filter, not on every render
    clearLocationCaches();
    if (process.env.NODE_ENV === 'development') {
    console.log('DEBUG: Location caches cleared before applying filter');
  }
    onFilter({
      searchTerm,
      category: category === "All Categories" ? null : category,
      location,
      radius: effectiveRadius,
      minBudget: budget[0],
      maxBudget: budget[1],
    });
  };

  const handleClear = () => {
    setSearchTerm("");
    setCategory("All Categories");
    setLocation("");
    setRadius(10);
    setBudget([0, 5000]);
    onFilter({});
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
      <div className="flex items-center justify-between mb-5">
        <h2 className="text-lg font-medium flex items-center">
          <Filter className="h-5 w-5 mr-2 text-classtasker-blue" />
          Filter Tasks
        </h2>
        <Button
          variant="ghost"
          size="sm"
          className="md:hidden"
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? <X size={18} /> : <Filter size={18} />}
        </Button>
      </div>

      <div className={`space-y-5 ${isOpen || "md:block hidden"}`}>
        <div>
          <Label htmlFor="search" className="text-sm font-medium mb-2 block flex items-center">
            <Search className="h-4 w-4 mr-1.5 text-gray-500" />
            Search
          </Label>
          <div className="relative">
            <Input
              id="search"
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="border-gray-300 focus:border-classtasker-blue focus:ring-classtasker-blue/20"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          <div className="space-y-2">
            <Label htmlFor="category" className="flex items-center">
              <Tag className="h-4 w-4 mr-1.5 text-gray-500" />
              Category
            </Label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger id="category" className="border-gray-300 focus:border-classtasker-blue focus:ring-classtasker-blue/20">
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {FILTER_CATEGORIES.map((cat) => (
                  <SelectItem key={cat} value={cat}>
                    {cat}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <LocationSearch
            value={location}
            onChange={handleLocationChange}
            onRadiusChange={handleRadiusChange}
            radius={radius}
            showRadius={true}
            className="space-y-2"
          />
        </div>

        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <Label htmlFor="budget" className="flex items-center">
              <PoundSterling className="h-4 w-4 mr-1.5 text-gray-500" />
              Budget Range
            </Label>
            <span className="text-sm font-medium bg-gray-100 px-2 py-1 rounded-md">
              £{budget[0]} - £{budget[1]}
            </span>
          </div>
          <Slider
            id="budget"
            defaultValue={[0, 5000]}
            min={0}
            max={5000}
            step={100}
            value={budget}
            onValueChange={setBudget}
            className="py-2"
          />
        </div>

        <div className="flex items-center justify-between pt-4 border-t border-gray-100 mt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleClear}
            className="text-gray-600 border-gray-300 hover:bg-gray-50"
          >
            <X className="h-4 w-4 mr-1.5" />
            Clear All
          </Button>
          <Button
            onClick={handleFilter}
            className="bg-classtasker-blue hover:bg-blue-600"
          >
            <Filter className="h-4 w-4 mr-1.5" />
            Apply Filters
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TaskFilter;

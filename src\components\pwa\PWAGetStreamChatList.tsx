import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  MessageSquare,
  RefreshCw,
  AlertCircle,
  WifiOff,
  Clock,
  Loader2
} from 'lucide-react';
import { StreamChat } from 'stream-chat';
import { getStreamClient, connectUser } from '@/integrations/getstream/client';
import { isOnline, registerConnectivityListeners, isPWA } from '@/utils/pwa-utils';
import PWAMobileLayout from './PWAMobileLayout';
import { supabase } from '@/integrations/supabase/client';

interface ChatChannel {
  id: string;
  name: string;
  taskId?: string;
  taskTitle?: string;
  taskStatus?: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
  isTaskChat?: boolean;
}

const PWAGetStreamChatList: React.FC = () => {
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMoreChannels, setHasMoreChannels] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [channels, setChannels] = useState<ChatChannel[]>([]);
  const [streamClient, setStreamClient] = useState<StreamChat | null>(null);
  const [offlineMode, setOfflineMode] = useState(!isOnline());

  // Effect to handle online/offline status
  useEffect(() => {
    const cleanup = registerConnectivityListeners(
      // Online callback
      () => {
        setOfflineMode(false);
        // Refresh data when coming back online
        if (streamClient) {
          fetchChannels(streamClient, 0, false);
        }
      },
      // Offline callback
      () => {
        setOfflineMode(true);
      }
    );

    return cleanup;
  }, [streamClient]);

  // Initialize GetStream client
  useEffect(() => {
    if (!user) return;

    let connectionAttempts = 0;
    const maxAttempts = 3;
    let mounted = true;

    const initializeStreamClient = async () => {
      try {
        if (!mounted) return;
        setLoading(true);
        setError(null);

        // Get user display name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        if (process.env.NODE_ENV === 'development') {
    console.log('[PWAGetStreamChatList] Connecting to GetStream...');
  }
        // Connect to GetStream
        const client = await connectUser(
          user.id,
          userName,
          user.id, // This is just a placeholder, the actual token is generated server-side
          profile?.avatar_url || undefined
        );

        if (!mounted) return;

        // Verify the client is connected before proceeding
        if (!client.isConnected()) {
          console.warn('[PWAGetStreamChatList] Client not connected after connectUser call');

          // Wait a moment and check again
          await new Promise(resolve => setTimeout(resolve, 1000));

          if (!client.isConnected()) {
            throw new Error('Failed to establish connection to GetStream');
          }
        }

        if (process.env.NODE_ENV === 'development') {
    console.log('[PWAGetStreamChatList] Successfully connected to GetStream');
  }
        setStreamClient(client);

        // Fetch channels (initial load)
        await fetchChannels(client, 0, false);
      } catch (error) {
        console.error('[PWAGetStreamChatList] Error initializing Stream client:', error);

        if (!mounted) return;

        connectionAttempts++;
        if (connectionAttempts < maxAttempts) {
          if (process.env.NODE_ENV === 'development') {
            console.log(`[PWAGetStreamChatList] Retrying connection (attempt ${connectionAttempts + 1}/${maxAttempts})...`);
            }
          // Wait before retrying
          setTimeout(initializeStreamClient, 2000);
        } else {
          setError('Failed to load chats. Please try again later.');
          setLoading(false);
        }
      }
    };

    initializeStreamClient();

    return () => {
      mounted = false;

      // In PWA mode, we should NOT disconnect the user to prevent the
      // "Error: You can't use a channel after client.disconnect() was called" issue
      const isPWA = typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches;

      if (!isPWA && streamClient) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWAGetStreamChatList] Cleaning up - disconnecting user');
          }
        streamClient.disconnectUser();
      } else if (streamClient) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWAGetStreamChatList] Cleaning up - NOT disconnecting user in PWA mode');
          }
      }
    };
  }, [user, profile]);

  // Fetch channels function with pagination support
  const fetchChannels = async (client: StreamChat, page = 0, append = false) => {
    try {
      // First, verify the client is connected
      if (!client.isConnected()) {
        console.warn('[PWAGetStreamChatList] Client not connected before fetching channels, attempting to reconnect');

        // Get user display name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user?.email?.split('@')[0] || 'User';

        // Try to reconnect
        await connectUser(
          user?.id || '',
          userName,
          user?.id || '', // This is just a placeholder, the actual token is generated server-side
          profile?.avatar_url || undefined
        );

        // Check again after reconnection attempt
        if (!client.isConnected()) {
          throw new Error('Client still not connected after reconnection attempt');
        }
      }

      if (process.env.NODE_ENV === 'development') {

        console.log('[PWAGetStreamChatList] Fetching channels for user: completed');


        }
      // Get user's organization for security filtering
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('organization_id, account_type')
        .eq('id', user.id)
        .single();

      // Fetch user's channels - use a more specific filter to ensure we get all task-related channels
      // We're looking for channels where:
      // 1. The user is a member
      // 2. The channel type is 'messaging'
      const filter = {
        type: 'messaging',
        members: { $in: [user?.id || ''] }
      };

      const sort = { last_message_at: -1 };

      // SMART PAGINATION: Get channel list with proper state for member filtering
      const pageSize = 20; // Mobile-friendly page size
      const offset = page * pageSize;

      if (process.env.NODE_ENV === 'development') {
    console.log(`[PWAGetStreamChatList] Loading page ${page}, offset ${offset}`);
  }
      const userChannels = await client.queryChannels(filter, sort, {
        limit: pageSize,     // ✅ Mobile-friendly page size
        offset: offset,      // ✅ Pagination offset
        state: true,         // ✅ Need state for member filtering to work properly
        watch: false,        // ✅ No real-time subscriptions for list
        message_limit: 1,    // ✅ Only last message for preview
      });

      if (process.env.NODE_ENV === 'development') {

        console.log(`[PWAGetStreamChatList] Page ${page}: Found ${userChannels.length} channels`);


        }
      // Check if there are more channels
      const hasMore = userChannels.length === pageSize;
      setHasMoreChannels(hasMore);

      // SECURITY: Filter channels based on organization membership (OPTIMIZED)
      const secureChannels = [];

      // Extract all task IDs from channels in one go
      const taskChannels = userChannels.filter(channel => channel.data?.task_id);
      const nonTaskChannels = userChannels.filter(channel => !channel.data?.task_id);

      // Add non-task channels (direct messages, etc.) immediately
      secureChannels.push(...nonTaskChannels);

      if (taskChannels.length > 0) {
        // Batch query all tasks at once instead of individual queries
        const taskIds = taskChannels.map(channel => channel.data.task_id);
        const { data: tasks, error: tasksError } = await supabase
          .from('tasks')
          .select('id, organization_id, visibility')
          .in('id', taskIds);

        if (tasksError) {
          console.error('[PWAGetStreamChatList] Error fetching tasks for security filtering:', tasksError);
          // If we can't validate, err on the side of caution and show no task channels
        } else if (tasks) {
          // Create a map for quick lookup
          const taskMap = new Map(tasks.map(task => [task.id, task]));

          // Process each channel with the pre-fetched task data
          for (const channel of taskChannels) {
            const taskId = channel.data.task_id;
            const task = taskMap.get(taskId);

            if (!task) {
              console.warn('[PWAGetStreamChatList] Task not found for channel:', channel.id);
              continue;
            }

            // Allow access if:
            // 1. User is in the same organization as the task
            // 2. User is a supplier and task is public
            const hasAccess = (
              userProfile?.organization_id === task.organization_id ||
              (userProfile?.account_type === 'supplier' && task.visibility === 'public')
            );

            if (hasAccess) {
              secureChannels.push(channel);
            } else {
              console.warn('[PWAGetStreamChatList] Filtering out unauthorized channel:', {
                channelId: channel.id,
                taskId,
                userOrg: userProfile?.organization_id,
                taskOrg: task.organization_id,
                userType: userProfile?.account_type,
                taskVisibility: task.visibility
              });

              // Note: We're not removing users from channels here to avoid rate limits
              // The removal will happen when they try to access the channel directly
            }
          }
        }
      }

      if (process.env.NODE_ENV === 'development') {

        console.log('[PWAGetStreamChatList] Successfully fetched channels: completed');

        }
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWAGetStreamChatList] Secure channels after filtering:', secureChannels.length);

        }
      // Debug: Log channel details to help troubleshoot
      secureChannels.forEach((channel, index) => {
        if (process.env.NODE_ENV === 'development') {
          console.log(`[PWAGetStreamChatList] Secure Channel ${index + 1}:`, {
          id: channel.id,
          name: channel.data?.name,
          taskId: channel.data?.task_id,
          members: channel.state.members ? Object.keys(channel.state.members) : [],
          messageCount: channel.state.messages.length,
          lastMessageAt: channel.data?.last_message_at,
        });
          }
      });

      // OPTIMIZATION: Skip duplicate task fetching - we already have task data from security filtering
      // We'll use lightweight channel names and update them on-demand when chats are opened
      if (process.env.NODE_ENV === 'development') {
        console.log('[PWAGetStreamChatList] Using lightweight approach - no duplicate task fetching');

        }
      // LIGHTWEIGHT FORMAT: Format channels for display without heavy operations
      const formattedChannels = secureChannels.map(channel => {
        const taskId = channel.data?.task_id;
        const isTaskChat = !!taskId || channel.id.startsWith('task-');

        // Extract the task ID from the channel ID if it starts with 'task-'
        const extractedTaskId = isTaskChat && !taskId && channel.id.startsWith('task-')
          ? channel.id.replace('task-', '')
          : null;

        const finalTaskId = taskId || extractedTaskId;

        // Use lightweight channel name (will be updated when chat is opened)
        let displayTitle = 'Chat';

        // Use existing channel name if available
        if (channel.data?.name && channel.data.name !== 'Chat') {
          displayTitle = channel.data.name;
        }
        // For task chats without names, use placeholder
        else if (isTaskChat) {
          displayTitle = 'Task Chat'; // Will be updated when chat is opened
        }

        // Get last message from lightweight data
        const lastMessage = channel.state?.messages?.[0]?.text ||
                           channel.data?.last_message_text ||
                           'No messages yet';

        return {
          id: channel.id,
          name: displayTitle,
          taskId: finalTaskId,
          taskTitle: displayTitle,
          taskStatus: undefined, // Will be loaded on-demand
          lastMessage,
          lastMessageTime: channel.data?.last_message_at,
          unreadCount: 0, // Will be calculated when chat is opened
          isTaskChat: isTaskChat
        };
      });

      // Sort channels: task chats first, then by last message time
      const sortedChannels = formattedChannels.sort((a, b) => {
        // First sort by whether it's a task chat
        if (a.isTaskChat && !b.isTaskChat) return -1;
        if (!a.isTaskChat && b.isTaskChat) return 1;

        // Then sort by last message time
        const timeA = a.lastMessageTime ? new Date(a.lastMessageTime).getTime() : 0;
        const timeB = b.lastMessageTime ? new Date(b.lastMessageTime).getTime() : 0;
        return timeB - timeA;
      });

      // Set channels based on whether we're appending or replacing
      if (append) {
        setChannels(prev => [...prev, ...sortedChannels]);
      } else {
        setChannels(sortedChannels);
      }

      setLoading(false);
      setError(null); // Clear any previous errors

      if (process.env.NODE_ENV === 'development') {
    console.log(`[PWAGetStreamChatList] Loaded ${sortedChannels.length} formatted channels for page ${page}`);
  }
    } catch (error) {
      console.error('[PWAGetStreamChatList] Error fetching channels:', error);

      // Check if this is a connection error
      const errorMessage = error.toString();
      if (errorMessage.includes('connectUser') || errorMessage.includes('connection')) {
        setError('Connection issue. Please refresh the page or try again later.');
      } else {
        setError('Failed to load chats. Please try again later.');
      }

      setLoading(false);
    }
  };

  // Load more channels function
  const loadMoreChannels = async () => {
    if (loadingMore || !hasMoreChannels || !streamClient) return;

    setLoadingMore(true);
    const nextPage = currentPage + 1;

    try {
      await fetchChannels(streamClient, nextPage, true);
      setCurrentPage(nextPage);
    } catch (error) {
      console.error('[PWAGetStreamChatList] Error loading more channels:', error);
      setError('Failed to load more channels. Please try again.');
    } finally {
      setLoadingMore(false);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);
    setCurrentPage(0);

    try {
      // If we don't have a stream client or it's not connected, reinitialize
      if (!streamClient || !streamClient.isConnected()) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[PWAGetStreamChatList] No connected client during refresh, reinitializing...');

          }
        // Get user display name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        // Connect to GetStream
        const client = await connectUser(
          user.id,
          userName,
          user.id,
          profile?.avatar_url || undefined
        );

        setStreamClient(client);
        await fetchChannels(client, 0, false);
      } else {
        // Use existing client
        await fetchChannels(streamClient, 0, false);
      }
    } catch (error) {
      console.error('[PWAGetStreamChatList] Error refreshing channels:', error);
      setError('Failed to refresh. Please try again.');
      setLoading(false);
    }
  };

  // Get initials for avatar
  const getInitials = (name: string): string => {
    if (!name) return 'C';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Format date
  const formatDate = (dateString?: string): string => {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Handle channel click
  const handleChannelClick = (channel: ChatChannel) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('[PWAGetStreamChatList] Navigating to channel:', channel.id);
      }
    navigate(`/messages/${channel.id}${channel.taskId ? `?task=${channel.taskId}` : ''}`);
  };

  // Prevent browser's native pull-to-refresh with targeted approach
  useEffect(() => {
    let startY = 0;
    let isAtTop = false;

    const handleTouchStart = (e: TouchEvent) => {
      startY = e.touches[0].clientY;
      isAtTop = window.scrollY === 0;
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (!isAtTop) return;

      const currentY = e.touches[0].clientY;
      const deltaY = currentY - startY;

      // Only prevent if we're pulling down (positive deltaY) and at the top
      if (deltaY > 0 && window.scrollY === 0) {
        e.preventDefault();
      }
    };

    // Only add to the specific container, not globally
    const container = document.querySelector('[data-chat-list-container]');
    if (container) {
      container.addEventListener('touchstart', handleTouchStart, { passive: false });
      container.addEventListener('touchmove', handleTouchMove, { passive: false });

      return () => {
        container.removeEventListener('touchstart', handleTouchStart);
        container.removeEventListener('touchmove', handleTouchMove);
      };
    }
  }, []);

  return (
    <PWAMobileLayout onRefresh={handleRefresh}>
      <div className="flex flex-col h-full" data-chat-list-container>
        {/* Removed header for better space usage - users know they're in messages from navigation context */}

        {/* Offline warning */}
        {offlineMode && (
          <div className="bg-yellow-50 p-2 text-center text-sm text-yellow-700 flex items-center justify-center">
            <WifiOff className="h-4 w-4 mr-2" />
            You're offline. Some features may be limited.
          </div>
        )}

        {/* Error state */}
        {error && (
          <div className="bg-red-50 border-b border-red-200 px-4 py-2 flex items-center">
            <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
            <span className="text-sm text-red-700">{error}</span>
          </div>
        )}



        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            // Loading state
            <div className="overflow-hidden bg-white">
              {Array(5).fill(0).map((_, i) => (
                <React.Fragment key={i}>
                  <div className="px-4 py-3">
                    <div className="flex items-center">
                      <Skeleton className="h-12 w-12 rounded-full mr-3 flex-shrink-0" />
                      <div className="flex-1">
                        <div className="flex justify-between items-baseline">
                          <Skeleton className="h-5 w-32" />
                          <Skeleton className="h-4 w-16 ml-2" />
                        </div>
                        <Skeleton className="h-4 w-48 mt-1" />
                      </div>
                    </div>
                  </div>
                  {i < 4 && (
                    <div className="h-px bg-gray-100 mx-4" />
                  )}
                </React.Fragment>
              ))}
            </div>
          ) : channels.length === 0 ? (
            // Empty state
            <div className="text-center py-12">
              <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-1">No messages yet</h3>
              <p className="text-gray-500">
                Your messages will appear here when you start chatting with others.
              </p>
            </div>
          ) : (
            // Channels list
            <div className="overflow-hidden bg-white">
              {channels.map((channel, index) => (
                <React.Fragment key={channel.id}>
                  <div
                    className="px-4 py-3 cursor-pointer hover:bg-gray-50 transition-colors"
                    onClick={() => handleChannelClick(channel)}
                  >
                    <div className="flex items-center">
                      <Avatar className="h-12 w-12 mr-3 flex-shrink-0">
                        <AvatarFallback>{getInitials(channel.name)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-baseline">
                          <h3 className="font-medium text-gray-900 truncate">
                            {channel.isTaskChat ? channel.taskTitle : channel.name}
                          </h3>
                          <span className="text-xs text-gray-400 ml-2 flex-shrink-0">
                            {formatDate(channel.lastMessageTime)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-500 truncate mt-1">{channel.lastMessage}</p>
                      </div>
                    </div>
                  </div>
                  {index < channels.length - 1 && (
                    <div className="h-px bg-gray-100 mx-4" />
                  )}
                </React.Fragment>
              ))}

              {/* Load More Button */}
              {hasMoreChannels && (
                <div className="p-4 border-t border-gray-100">
                  <Button
                    variant="outline"
                    onClick={loadMoreChannels}
                    disabled={loadingMore}
                    className="w-full py-3 text-sm font-medium"
                  >
                    {loadingMore ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Loading more conversations...
                      </>
                    ) : (
                      'Load More Conversations'
                    )}
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </PWAMobileLayout>
  );
};

export default PWAGetStreamChatList;
